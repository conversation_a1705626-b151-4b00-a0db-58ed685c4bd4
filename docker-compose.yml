services:
  # postgres:
  #   image: postgres:15
  #   container_name: postgres
  #   restart: unless-stopped
  #   ports:
  #     - "5432:5432"
  #   environment:
  #     POSTGRES_USER: dev_db_pg_owner
  #     POSTGRES_PASSWORD: your_local_pg_password
  #     POSTGRES_DB: dev
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data

  supertokens:
    image: supertokens/supertokens-postgresql
    container_name: supertokens
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_USER: dev_db_pg_owner
      POSTGRESQL_PASSWORD: Ce3Y5YqA78qGH@E_cadet_root
      POSTGRESQL_HOST: cl-pab-dev-sindia-db-1.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      POSTGRESQL_DATABASE_NAME: dev-anooj
      POSTGRESQL_TABLE_SCHEMA: auth

    # depends_on:
    #   - postgres

volumes:
  postgres_data:
