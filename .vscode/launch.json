{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug API (TypeScript)", "runtimeExecutable": "pnpm", "runtimeArgs": ["turbo", "run", "dev", "--filter=api"], "skipFiles": ["<node_internals>/**"], "cwd": "${workspaceFolder}/apps/api", "outFiles": ["${workspaceFolder}/apps/api/dist/**/*.js"], "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/apps/api/dist/**/*.js", "${workspaceFolder}/apps/api/src/**/*.ts"], "env": {"NODE_ENV": "development"}}]}