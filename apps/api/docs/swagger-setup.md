# Swagger/OpenAPI Setup Guide

This guide explains how to use the Zod OpenAPI and Swagger setup for the Auth Controller and how to extend it to other controllers.

## Overview

The API documentation is automatically generated from Zod schemas with OpenAPI metadata. This provides:

- **Type-safe schemas** with runtime validation
- **Automatic API documentation** with Swagger UI
- **Consistent response formats** across all endpoints
- **Interactive API testing** through Swagger UI

## Accessing the Documentation

Once the server is running, you can access:

- **Swagger UI**: `http://localhost:5001/api-docs`
- **OpenAPI JSON**: `http://localhost:5001/api-docs.json`
- **OpenAPI YAML**: `http://localhost:5001/api-docs.yaml`

## File Structure

```
src/
├── core/
│   ├── openapi.ts          # OpenAPI configuration and utilities
│   ├── swagger.ts          # Swagger UI setup
│   └── Responses.ts        # Standardized response classes
└── modules/
    └── Auth/
        ├── AuthController.ts    # Controller with endpoints
        └── auth.openapi.ts      # OpenAPI schemas and paths
```

## How It Works

### 1. Schema Definition with OpenAPI Metadata

In `auth.openapi.ts`, schemas are enhanced with OpenAPI metadata:

```typescript
export const SignInRequestSchema = z.object({
  formFields: z.array(
    z.object({
      id: z.string().openapi({ 
        example: "email",
        description: "Field identifier (email or password)" 
      }),
      value: z.string().openapi({ 
        example: "<EMAIL>",
        description: "Field value" 
      }),
    })
  ).length(2).openapi({
    description: "Array of form fields containing email and password",
    example: [
      { id: "email", value: "<EMAIL>" },
      { id: "password", value: "password123" }
    ]
  }),
}).openapi({
  description: "Sign in request payload"
});
```

### 2. Path Definitions

Each endpoint is defined with complete OpenAPI specification:

```typescript
export const authPaths = {
  "/auth/me": {
    get: {
      tags: ["Authentication"],
      summary: "Get current user information",
      description: "Retrieve information about the currently authenticated user",
      security: [{ bearerAuth: [] }, { cookieAuth: [] }],
      responses: {
        "200": {
          description: "User information retrieved successfully",
          content: {
            "application/json": {
              schema: createApiResponse(UserInfoResponseSchema, "User information"),
            },
          },
        },
        "401": {
          description: "Unauthorized - user not authenticated",
          content: {
            "application/json": {
              schema: createErrorResponse("Unauthorized", 401),
            },
          },
        },
      },
    },
  },
};
```

### 3. Schema Registration

All schemas and paths are registered with the OpenAPI registry:

```typescript
// Register schemas
registry.register("SignInRequest", SignInRequestSchema);
registry.register("UserInfoResponse", UserInfoResponseSchema);

// Register paths
Object.entries(authPaths).forEach(([path, methods]) => {
  registry.registerPath({
    method: Object.keys(methods)[0] as any,
    path,
    ...Object.values(methods)[0] as any,
  });
});
```

## Adding Documentation to New Controllers

### Step 1: Create OpenAPI Schema File

Create a new file `[module].openapi.ts` in your module directory:

```typescript
// src/modules/Users/<USER>
import { z } from "zod";
import { registry, createApiResponse, createErrorResponse } from "../../core/openapi";

// Define request/response schemas with OpenAPI metadata
export const CreateUserRequestSchema = z.object({
  firstName: z.string().openapi({
    example: "John",
    description: "User's first name"
  }),
  lastName: z.string().openapi({
    example: "Doe", 
    description: "User's last name"
  }),
  email: z.string().email().openapi({
    example: "<EMAIL>",
    description: "User's email address"
  }),
}).openapi({
  description: "Create user request payload"
});

// Define paths
export const userPaths = {
  "/users": {
    post: {
      tags: ["Users"],
      summary: "Create a new user",
      description: "Create a new user account",
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: CreateUserRequestSchema,
          },
        },
      },
      responses: {
        "201": {
          description: "User created successfully",
          content: {
            "application/json": {
              schema: createApiResponse(UserResponseSchema, "User created successfully"),
            },
          },
        },
        "400": {
          description: "Validation error",
          content: {
            "application/json": {
              schema: createValidationErrorResponse(),
            },
          },
        },
      },
    },
  },
};

// Register schemas and paths
registry.register("CreateUserRequest", CreateUserRequestSchema);
Object.entries(userPaths).forEach(([path, methods]) => {
  registry.registerPath({
    method: Object.keys(methods)[0] as any,
    path,
    ...Object.values(methods)[0] as any,
  });
});
```

### Step 2: Import in Swagger Setup

Add the import to `src/core/swagger.ts`:

```typescript
// Import all OpenAPI definitions to register them
import "../modules/Auth/auth.openapi";
import "../modules/Users/<USER>"; // Add this line
```

## Available Utilities

### Response Helpers

- `createApiResponse(schema, description, example?)` - Creates success response schema
- `createErrorResponse(description, statusCode)` - Creates error response schema  
- `createValidationErrorResponse()` - Creates validation error response schema

### Standard Response Classes

- `BaseResponse.success()` - Send success response
- `BaseResponse.created()` - Send 201 created response
- `ErrorResponse.badRequest()` - Send 400 error
- `ErrorResponse.unauthorized()` - Send 401 error
- `ErrorResponse.notFound()` - Send 404 error
- `ErrorResponse.conflict()` - Send 409 error
- `ErrorResponse.validationError()` - Send validation error with field details

## Authentication in Swagger

The setup includes two authentication schemes:

1. **Bearer Token**: Add `Authorization: Bearer <token>` header
2. **Cookie Auth**: Uses `sAccessToken` cookie (SuperTokens default)

Users can authenticate in Swagger UI using the "Authorize" button.

## Best Practices

1. **Always add OpenAPI metadata** to your Zod schemas with examples and descriptions
2. **Use consistent response formats** with the provided response classes
3. **Group endpoints by tags** for better organization in Swagger UI
4. **Include comprehensive error responses** for all possible scenarios
5. **Add security requirements** to protected endpoints
6. **Use descriptive summaries and descriptions** for better API documentation

## Customization

### Styling

The Swagger UI includes custom CSS for better appearance. You can modify the styles in `src/core/swagger.ts` in the `customSwaggerCSS` constant.

### Server Configuration

Update the server URLs in `src/core/openapi.ts`:

```typescript
servers: [
  {
    url: "http://localhost:5001",
    description: "Development server",
  },
  {
    url: "https://api.yourdomain.com",
    description: "Production server",
  },
],
```

### API Information

Update the API information in `src/core/openapi.ts`:

```typescript
info: {
  version: "1.0.0",
  title: "Your API Title",
  description: "Your API description",
  contact: {
    name: "API Support",
    email: "<EMAIL>",
  },
},
```

## Troubleshooting

### Common Issues

1. **Schemas not appearing**: Make sure to import your `.openapi.ts` file in `swagger.ts`
2. **Type errors**: Ensure all Zod schemas have proper OpenAPI metadata
3. **Missing endpoints**: Verify that paths are registered with the registry
4. **Authentication not working**: Check that security schemes are properly configured

### Debugging

- Check the console for any registration errors when starting the server
- Visit `/api-docs.json` to see the raw OpenAPI specification
- Use browser dev tools to inspect any client-side errors in Swagger UI
