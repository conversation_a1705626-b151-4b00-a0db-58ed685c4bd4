{"name": "api", "version": "0.0.0", "type": "module", "private": true, "scripts": {"start": "node dist/index.cjs", "dev": "prisma generate && tsup --watch --onSuccess \"node dist/index.cjs\"", "build": "tsup", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "check-types": "tsc --noEmit", "lint": "eslint src/ --max-warnings 0", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@repo/jest-presets/node"}, "dependencies": {"@azure/communication-email": "^1.0.0", "@prisma/client": "^6.17.0", "@repo/database": "*", "@repo/logger": "*", "@repo/validation": "*", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "4.21.2", "js-yaml": "^4.1.0", "morgan": "^1.10.0", "openapi3-ts": "^4.5.0", "prisma": "^6.17.0", "reflect-metadata": "^0.2.2", "routing-controllers": "^0.11.3", "supertokens-node": "^21.0.2", "swagger-ui-express": "^5.0.1", "zod": "^4.1.12", "zod-openapi": "^5.4.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "@repo/typescript-config": "*", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/js-yaml": "^4.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.8", "eslint": "^9.31.0", "jest": "^29.7.0", "supertest": "^7.1.0", "tsup": "^8.5.0", "typescript": "5.8.2"}}