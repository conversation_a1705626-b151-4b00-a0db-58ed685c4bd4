generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["app"]
}

model Tenant {
  id               String       @id @default(uuid()) @db.Uuid
  tenantId         String       @unique @map("tenant_id") @db.VarChar(255)
  companyName      String       @map("company_name") @db.VarChar(255)
  adminName        String       @map("admin_name") @db.VarChar(255)
  emailId          String       @unique @map("email_id") @db.VarChar(255)
  subscriptionType String       @map("subscription_type") @db.VarChar(50)
  contactNo        String       @map("contact_no") @db.VarChar(15)
  capOnUsers       Int          @map("cap_on_users") @db.Integer
  address          String       @db.Text
  type             TenantType   @default(normal)
  status           TenantStatus @default(active)
  firstTimeUser    Bo<PERSON>an      @default(true) @map("first_time_user") @db.Boolean
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime     @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  users       User[]
  userLookups UserLookup[]

  @@map("tenant")
  @@schema("app")
}

model UserLookup {
  emailId  String @id @map("email_id") @db.VarChar(255)
  tenantId String @map("tenant_id") @db.VarChar(255)

  // Back-reference to tenant
  tenant Tenant @relation(fields: [tenantId], references: [tenantId], onDelete: Cascade)

  @@map("user_lookup")
  @@schema("app")
}

// Enum for Tenant type
enum TenantType {
  platform
  normal

  @@schema("app")
}

model User {
  id            String     @id @default(uuid()) @db.Uuid
  firstName     String     @map("first_name") @db.VarChar(100)
  lastName      String?    @map("last_name") @db.VarChar(100)
  displayName   String     @map("display_name") @db.VarChar(200)
  emailId       String     @unique @map("email_id") @db.VarChar(255)
  role          UserRole   @default(project_handler)
  status        UserStatus @default(pending_verification)
  tenantId      String     @map("tenant_id") @db.VarChar(255)
  firstTimeUser Boolean    @default(true) @map("first_time_user") @db.Boolean
  lastLoginAt   DateTime?  @map("last_login_at") @db.Timestamp(6)
  createdAt     DateTime   @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt     DateTime   @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [tenantId])

  @@map("user")
  @@schema("app")
}

// Enum for user roles
enum UserRole {
  superadmin // platform tenant user, can manage everything
  admin // admin for a normal tenant
  project_handler // normal user

  @@schema("app")
}

// Enum for user status
enum UserStatus {
  pending_verification
  active
  inactive

  @@schema("app")
}

// Enum for tenant status
enum TenantStatus {
  active
  inactive

  @@schema("app")
}
