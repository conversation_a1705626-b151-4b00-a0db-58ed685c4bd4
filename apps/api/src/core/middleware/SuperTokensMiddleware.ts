import { Request, Response, NextFunction } from "express";
import { verifySession } from "supertokens-node/recipe/session/framework/express";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { getSuperTokensService } from "../services/SuperTokensService";
import { logError, logWarn } from "@repo/logger";

/**
 * Middleware to verify SuperTokens session
 */
export const requireAuth = () => {
  return verifySession({ checkDatabase: true });
};

/**
 * Middleware to optionally verify SuperTokens session
 */
export const optionalAuth = () => {
  return verifySession({ sessionRequired: false });
};

/**
 * Middleware to add user metadata to request
 */
export const withUserMetadata = async (req: any, res: Response, next: NextFunction) => {
  try {
    if (req.session) {
      const userId = req.session.getUserId();
      const metadata = await UserMetadata.getUserMetadata(userId);
      req.userMetadata = metadata.metadata;
    }
    next();
  } catch (error) {
    logError("Failed to fetch user metadata", error as Error);
    res.status(500).json({ error: "Failed to fetch user metadata" });
  }
};

/**
 * Middleware to check user roles (requires metadata middleware)
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: any, res: Response, next: NextFunction) => {
    try {
      if (!req.userMetadata) {
        logWarn("User metadata not found - ensure withUserMetadata middleware is used first");
        return res.status(403).json({ error: "Access denied - no user metadata" });
      }

      const userRole = req.userMetadata.role;
      if (!userRole || !allowedRoles.includes(userRole)) {
        logWarn("Access denied - insufficient role", {
          userRole,
          allowedRoles,
          userId: req.session?.getUserId(),
        });
        return res.status(403).json({ error: "Access denied - insufficient permissions" });
      }

      next();
    } catch (error) {
      logError("Role check failed", error as Error);
      res.status(500).json({ error: "Authorization check failed" });
    }
  };
};

/**
 * Middleware to check if user is active (requires metadata middleware)
 */
export const requireActiveUser = (req: any, res: Response, next: NextFunction) => {
  try {
    if (!req.userMetadata) {
      logWarn("User metadata not found - ensure withUserMetadata middleware is used first");
      return res.status(403).json({ error: "Access denied - no user metadata" });
    }

    const isActive = req.userMetadata.isActive;
    if (isActive === false) {
      logWarn("Access denied - user is deactivated", {
        userId: req.session?.getUserId(),
      });
      return res.status(403).json({ error: "Account is deactivated. Please contact support." });
    }

    next();
  } catch (error) {
    logError("Active user check failed", error as Error);
    res.status(500).json({ error: "User status check failed" });
  }
};

/**
 * Middleware to add SuperTokens middleware to Express app
 */
export const superTokensMiddleware = () => {
  const superTokensService = getSuperTokensService();
  return superTokensService.getMiddleware();
};

/**
 * Error handler for SuperTokens
 */
export const superTokensErrorHandler = () => {
  const superTokensService = getSuperTokensService();
  return superTokensService.getErrorHandler();
};
