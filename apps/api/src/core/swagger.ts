import { Express } from "express";
import swaggerUi from "swagger-ui-express";

import { authApiDocument } from "@/modules/Auth/auth.openapi";

export function setupSwagger(app: Express) {
  const openApiDocument = {
    ...authApiDocument,
  };
  // Swagger UI options
  const swaggerOptions = {
    explorer: true,
    swaggerOptions: {
      docExpansion: "none",
      filter: true,
      showRequestDuration: true,
      tryItOutEnabled: true,
    },
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info { margin: 20px 0 }
      .swagger-ui .info .title { color: #3b82f6 }
      .swagger-ui .scheme-container { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0 }
    `,
    customSiteTitle: "Cadet Labs API Documentation",
    customfavIcon: "/favicon.ico",
  };

  // Serve Swagger UI
  app.use("/api-docs", swaggerUi.serve);
  app.get("/api-docs", swaggerUi.setup(openApiDocument, swaggerOptions));

  // Serve raw OpenAPI JSON
  app.get("/api-docs.json", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(openApiDocument);
  });

  // Serve OpenAPI YAML
  app.get("/api-docs.yaml", (req, res) => {
    const yaml = require("js-yaml");
    res.setHeader("Content-Type", "text/yaml");
    res.send(yaml.dump(openApiDocument));
  });

  console.log("📚 Swagger UI available at: http://localhost:3005/api-docs");
  console.log("📄 OpenAPI JSON available at: http://localhost:3005/api-docs.json");
}
