import supertokens from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { middleware, errorHandler } from "supertokens-node/framework/express";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { logInfo, logError, logWarn } from "@repo/logger";

export interface SuperTokensConfig {
  connectionURI: string;
  appInfo: {
    appName: string;
    apiDomain: string;
    websiteDomain: string;
    apiBasePath?: string;
    websiteBasePath?: string;
  };
}

export class SuperTokensService {
  private config: SuperTokensConfig;
  private initialized = false;

  constructor(config: SuperTokensConfig) {
    this.config = config;
  }

  /**
   * Initialize SuperTokens with the provided configuration
   */
  init(): void {
    try {
      supertokens.init({
        framework: "express",
        supertokens: {
          connectionURI: this.config.connectionURI,
        },
        appInfo: this.config.appInfo,
        recipeList: [
          EmailVerification.init({
            mode: "REQUIRED", // or "OPTIONAL"
          }),
          EmailPassword.init({
            override: {
              apis: (originalImplementation) => {
                return {
                  ...originalImplementation,
                  signInPOST: async (input) => {
                    if (originalImplementation.signInPOST === undefined) {
                      throw new Error("signInPOST not implemented in originalImplementation");
                    }

                    logInfo("SuperTokens signin attempt", {
                      email: input.formFields.find((f) => f.id === "email")?.value,
                    });
                    const email = input.formFields.find((f) => f.id === "email")?.value;

                    // TODO: Add custom user validation logic here
                    // const isActive = await this.checkUserIsActive(email as string);
                    // if (!isActive) {
                    //   return {
                    //     status: "CUSTOM_ERROR",
                    //     message: "User is deactivated. Please contact support.",
                    //   };
                    // }

                    // Call original implementation
                    return await originalImplementation.signInPOST(input);
                  },
                  signUpPOST: undefined, // Disable signup if needed
                };
              },
            },
          }),
          UserMetadata.init(),
          Session.init({
            cookieSecure: process.env.NODE_ENV === "production",
            cookieSameSite: "lax",
            exposeAccessTokenToFrontendInCookieBasedAuth: true,
          }),
        ],
      });

      this.initialized = true;
      logInfo("SuperTokens initialized successfully");
    } catch (error) {
      logError("Failed to initialize SuperTokens", error as Error);
      throw error;
    }
  }

  /**
   * Get SuperTokens middleware
   */
  getMiddleware() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return middleware();
  }

  /**
   * Get SuperTokens error handler
   */
  getErrorHandler() {
    return errorHandler();
  }

  /**
   * Check if SuperTokens is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

/**
 * Create default SuperTokens service instance
 */
export function createDefaultSuperTokensService(): SuperTokensService {
  const config: SuperTokensConfig = {
    connectionURI: process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567",
    appInfo: {
      appName: process.env.SUPERTOKENS_APP_NAME || "API",
      apiDomain: process.env.SUPERTOKENS_API_DOMAIN || "http://localhost:3005",
      websiteDomain: process.env.SUPERTOKENS_WEBSITE_DOMAIN || "http://localhost:3000",
      apiBasePath: "/auth",
      websiteBasePath: "/auth",
    },
  };

  return new SuperTokensService(config);
}

// Singleton instance
let superTokensInstance: SuperTokensService | null = null;

/**
 * Get or create SuperTokens service singleton
 */
export function getSuperTokensService(): SuperTokensService {
  if (!superTokensInstance) {
    superTokensInstance = createDefaultSuperTokensService();
  }
  return superTokensInstance;
}
