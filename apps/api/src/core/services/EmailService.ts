import { EmailClient } from "@azure/communication-email";
import * as fs from "fs/promises";
import * as path from "path";
import { logInfo, logError, logWarn } from "@repo/logger";

export interface EmailContent {
  to: string;
  subject: string;
  data: Record<string, any>;
}

export class EmailService {
  private static emailClient: EmailClient;
  private static templatesPath: string = path.join(__dirname, "../../../templates");

  /**
   * Initialize the email client (call this once in your application startup)
   */
  static initialize(connectionString: string): void {
    try {
      this.emailClient = new EmailClient(connectionString);
      logInfo("Email service initialized successfully");
    } catch (error) {
      logError("Failed to initialize email service", error as Error);
      throw error;
    }
  }

  /**
   * Send email with HTML template
   */
  static async sendEmailWithTemplate(
    content: EmailContent,
    templateName: string
  ): Promise<void> {
    try {
      // Ensure email client is initialized
      if (!this.emailClient) {
        throw new Error("EmailClient not initialized. Call EmailService.initialize() first.");
      }

      // Read the HTML template
      const htmlTemplate = await this.readTemplate(templateName);

      // Replace placeholders in the template with actual data
      const htmlContent = this.replacePlaceholders(htmlTemplate, content.data);

      // Prepare the email message
      const emailMessage = {
        senderAddress: process.env.SENDER_EMAIL || "<EMAIL>",
        content: {
          subject: content.subject,
          html: htmlContent,
        },
        recipients: {
          to: [
            {
              address: content.to,
            },
          ],
        },
      };

      // Send the email
      const poller = await this.emailClient.beginSend(emailMessage);
      const result = await poller.pollUntilDone();

      logInfo("Email sent successfully", { 
        to: content.to, 
        subject: content.subject,
        messageId: result.id 
      });
    } catch (error) {
      logError("Failed to send email", { 
        error: error as Error, 
        to: content.to, 
        subject: content.subject 
      });
      throw error;
    }
  }

  /**
   * Send plain text email
   */
  static async sendPlainEmail(
    to: string,
    subject: string,
    text: string
  ): Promise<void> {
    try {
      if (!this.emailClient) {
        throw new Error("EmailClient not initialized. Call EmailService.initialize() first.");
      }

      const emailMessage = {
        senderAddress: process.env.SENDER_EMAIL || "<EMAIL>",
        content: {
          subject,
          plainText: text,
        },
        recipients: {
          to: [{ address: to }],
        },
      };

      const poller = await this.emailClient.beginSend(emailMessage);
      const result = await poller.pollUntilDone();

      logInfo("Plain email sent successfully", { 
        to, 
        subject,
        messageId: result.id 
      });
    } catch (error) {
      logError("Failed to send plain email", { 
        error: error as Error, 
        to, 
        subject 
      });
      throw error;
    }
  }

  /**
   * Read HTML template from file
   */
  private static async readTemplate(templateName: string): Promise<string> {
    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.html`);
      const template = await fs.readFile(templatePath, "utf-8");
      return template;
    } catch (error) {
      logError("Failed to read email template", { 
        error: error as Error, 
        templateName 
      });
      throw new Error(`Template ${templateName} not found`);
    }
  }

  /**
   * Replace placeholders in template with actual data
   */
  private static replacePlaceholders(
    template: string,
    data: Record<string, any>
  ): string {
    let result = template;
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = new RegExp(`{{${key}}}`, "g");
      result = result.replace(placeholder, String(value));
    }
    
    return result;
  }

  /**
   * Check if email service is initialized
   */
  static isInitialized(): boolean {
    return !!this.emailClient;
  }

  /**
   * Set custom templates path
   */
  static setTemplatesPath(path: string): void {
    this.templatesPath = path;
    logInfo("Email templates path updated", { path });
  }
}
