import { Request, Response, NextFunction } from "express";
import { z, ZodType } from "zod";
import { ErrorResponse, ValidationError } from "../Responses";

/**
 * Middleware factory for Zod validation
 * Validates request body, query parameters, or params against a Zod schema
 */
export function validateBody<T>(schema: z.ZodType<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors: ValidationError[] = error.issues.map((err) => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
        }));
        return ErrorResponse.validationError(
          res,
          validationErrors,
          "Request body validation failed"
        );
      }
      next(error);
    }
  };
}

/**
 * Middleware factory for validating query parameters
 */
export function validateQuery<T>(schema: z.ZodType<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.query);
      req.query = validatedData as any;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors: ValidationError[] = error.issues.map((err) => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
        }));
        return ErrorResponse.validationError(
          res,
          validationErrors,
          "Query parameters validation failed"
        );
      }
      next(error);
    }
  };
}

/**
 * Middleware factory for validating URL parameters
 */
export function validateParams<T>(schema: z.ZodType<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.params);
      req.params = validatedData as any;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors: ValidationError[] = error.issues.map((err) => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
        }));
        return ErrorResponse.validationError(
          res,
          validationErrors,
          "URL parameters validation failed"
        );
      }
      next(error);
    }
  };
}

/**
 * Utility function to shape response data using Zod schema
 * This ensures consistent response format and removes unwanted fields
 */
export function shapeResponse<T>(data: unknown, schema: z.ZodType<T>): T {
  return schema.parse(data);
}

/**
 * Utility function to shape array responses
 */
export function shapeArrayResponse<T>(data: unknown[], schema: z.ZodType<T>): T[] {
  return data.map((item) => schema.parse(item));
}

/**
 * Direct validation utility function (not middleware)
 * Use this for validating data directly in controllers
 */
export function validateData<T>(schema: z.ZodType<T>, data: unknown): T {
  return schema.parse(data);
}
