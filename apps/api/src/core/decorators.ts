/**
 * Re-export routing-controllers decorators for convenience
 * This allows for a consistent import path across the application
 */

// Controller decorators
export { <PERSON>, JsonController } from "routing-controllers";

// Route decorators
export { Get, Post, Put, Delete, Patch, Head, All } from "routing-controllers";

// Parameter decorators
export {
  Param,
  Body,
  QueryParam,
  Header,
  Req,
  Res,
  Session,
  State,
  UploadedFile,
  UploadedFiles,
} from "routing-controllers";

// Middleware decorators
export { UseBefore, UseAfter, Middleware, UseInterceptor } from "routing-controllers";

// Validation decorators
export {
  IsEmail,
  IsString,
  IsNumber,
  IsOptional,
  MinLength,
  MaxLength,
  IsNotEmpty,
} from "class-validator";

// Response utilities
export { BaseResponse, ErrorResponse, ResponseHelper } from "./Responses";

// Transform decorators
export { Transform, Type, Exclude, Expose } from "class-transformer";
