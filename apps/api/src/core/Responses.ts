import { Response } from "express";

/**
 * Standard API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  errors?: ValidationError[];
  meta?: ResponseMeta;
}

/**
 * Validation error structure
 */
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

/**
 * Response metadata for pagination, etc.
 */
export interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
  [key: string]: any;
}

/**
 * Base class for standardized API responses
 */
export class BaseResponse {
  /**
   * Send a successful response
   */
  static success<T>(
    res: Response,
    data?: T,
    message?: string,
    statusCode: number = 200,
    meta?: ResponseMeta
  ): Response {
    const response: ApiResponse<T> = {
      success: true,
      message,
      data,
      meta,
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send a created response (201)
   */
  static created<T>(
    res: Response,
    data?: T,
    message: string = "Resource created successfully"
  ): Response {
    return this.success(res, data, message, 201);
  }

  /**
   * Send a no content response (204)
   */
  static noContent(res: Response): Response {
    return res.status(204).send();
  }
}

/**
 * Base class for standardized error responses
 */
export class ErrorResponse {
  /**
   * Send a generic error response
   */
  static error(
    res: Response,
    message: string = "An error occurred",
    statusCode: number = 500,
    error?: string
  ): Response {
    const response: ApiResponse = {
      success: false,
      message,
      error,
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send a bad request response (400)
   */
  static badRequest(
    res: Response,
    message: string = "Bad request",
    error?: string
  ): Response {
    return this.error(res, message, 400, error);
  }

  /**
   * Send a validation error response (400)
   */
  static validationError(
    res: Response,
    errors: ValidationError[],
    message: string = "Validation failed"
  ): Response {
    const response: ApiResponse = {
      success: false,
      message,
      errors,
    };

    return res.status(400).json(response);
  }

  /**
   * Send an unauthorized response (401)
   */
  static unauthorized(
    res: Response,
    message: string = "Unauthorized",
    error?: string
  ): Response {
    return this.error(res, message, 401, error);
  }

  /**
   * Send a forbidden response (403)
   */
  static forbidden(
    res: Response,
    message: string = "Forbidden",
    error?: string
  ): Response {
    return this.error(res, message, 403, error);
  }

  /**
   * Send a not found response (404)
   */
  static notFound(
    res: Response,
    message: string = "Resource not found",
    error?: string
  ): Response {
    return this.error(res, message, 404, error);
  }

  /**
   * Send a conflict response (409)
   */
  static conflict(
    res: Response,
    message: string = "Conflict",
    error?: string
  ): Response {
    return this.error(res, message, 409, error);
  }

  /**
   * Send an unprocessable entity response (422)
   */
  static unprocessableEntity(
    res: Response,
    message: string = "Unprocessable entity",
    error?: string
  ): Response {
    return this.error(res, message, 422, error);
  }

  /**
   * Send an internal server error response (500)
   */
  static internalServerError(
    res: Response,
    message: string = "Internal server error",
    error?: string
  ): Response {
    return this.error(res, message, 500, error);
  }

  /**
   * Send a service unavailable response (503)
   */
  static serviceUnavailable(
    res: Response,
    message: string = "Service unavailable",
    error?: string
  ): Response {
    return this.error(res, message, 503, error);
  }
}

/**
 * Utility class for common response patterns
 */
export class ResponseHelper {
  /**
   * Handle async controller operations with standardized error handling
   */
  static async handleAsync<T>(
    res: Response,
    operation: () => Promise<T>,
    successMessage?: string,
    successStatusCode?: number
  ): Promise<Response> {
    try {
      const result = await operation();
      return BaseResponse.success(res, result, successMessage, successStatusCode);
    } catch (error) {
      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes("not found")) {
          return ErrorResponse.notFound(res, error.message);
        }
        if (error.message.includes("already exists")) {
          return ErrorResponse.conflict(res, error.message);
        }
        if (error.message.includes("validation")) {
          return ErrorResponse.badRequest(res, error.message);
        }
        
        return ErrorResponse.internalServerError(res, "An unexpected error occurred", error.message);
      }
      
      return ErrorResponse.internalServerError(res);
    }
  }

  /**
   * Create paginated response with metadata
   */
  static paginated<T>(
    res: Response,
    data: T[],
    page: number,
    limit: number,
    total: number,
    message?: string
  ): Response {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const meta: ResponseMeta = {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
    };

    return BaseResponse.success(res, data, message, 200, meta);
  }
}
