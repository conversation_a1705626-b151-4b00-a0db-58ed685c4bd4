import { createDocument } from "zod-openapi";
import { z } from "zod";
import yaml from "js-yaml";
import { HealthCheckResponseSchema } from "@/modules/Auth/auth.openapi";

// Base response schemas with OpenAPI metadata using .meta()
export const SuccessResponseSchema = z
  .object({
    success: z.boolean().meta({ example: true }),
    message: z.string().optional().meta({ example: "Operation completed successfully" }),
    data: z.any().optional(),
    meta: z
      .object({
        page: z.number().optional(),
        limit: z.number().optional(),
        total: z.number().optional(),
        totalPages: z.number().optional(),
        hasNext: z.boolean().optional(),
        hasPrev: z.boolean().optional(),
      })
      .optional(),
  })
  .meta({
    description: "Successful API response",
  });

export const ErrorResponseSchema = z
  .object({
    success: z.boolean().meta({ example: false }),
    message: z.string().meta({ example: "An error occurred" }),
    error: z.string().optional().meta({ example: "ERROR_CODE" }),
    errors: z
      .array(
        z.object({
          field: z.string().meta({ example: "email" }),
          message: z.string().meta({ example: "Invalid email format" }),
          code: z.string().optional().meta({ example: "invalid_email" }),
        })
      )
      .optional(),
  })
  .meta({
    description: "Error response",
  });

export const ValidationErrorResponseSchema = z
  .object({
    success: z.boolean().meta({ example: false }),
    message: z.string().meta({ example: "Validation failed" }),
    errors: z.array(
      z.object({
        field: z.string().meta({ example: "email" }),
        message: z.string().meta({ example: "Invalid email format" }),
        code: z.string().optional().meta({ example: "invalid_email" }),
      })
    ),
  })
  .meta({
    description: "Validation error response",
  });

// Helper functions to create API responses
export function createApiResponse<T extends z.ZodTypeAny>(
  dataSchema: T,
  message: string = "Success"
) {
  return z
    .object({
      success: z.literal(true).meta({ example: true }),
      message: z.string().meta({ example: message }),
      data: dataSchema,
      meta: z
        .object({
          page: z.number().optional(),
          limit: z.number().optional(),
          total: z.number().optional(),
          totalPages: z.number().optional(),
          hasNext: z.boolean().optional(),
          hasPrev: z.boolean().optional(),
        })
        .optional(),
    })
    .meta({
      description: `Successful response with ${message.toLowerCase()}`,
    });
}

export function createErrorResponse(message: string, statusCode: number) {
  return z
    .object({
      success: z.literal(false).meta({ example: false }),
      message: z.string().meta({ example: message }),
      error: z
        .string()
        .optional()
        .meta({ example: `HTTP_${statusCode}` }),
    })
    .meta({
      description: `Error response: ${message}`,
    });
}

export function createValidationErrorResponse() {
  return ValidationErrorResponseSchema;
}
