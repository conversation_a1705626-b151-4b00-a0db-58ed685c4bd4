import { PrismaClient } from "@prisma/client";
import { getPrisma } from "@/database";

/**
 * Base repository class that provides common database operations
 * All model repositories should extend this class
 */
export abstract class BaseRepository<T, CreateInput, UpdateInput> {
  protected prisma: PrismaClient;

  constructor() {
    this.prisma = getPrisma();
  }

  /**
   * Find all records
   */
  abstract findAll(): Promise<T[]>;

  /**
   * Find a record by ID
   */
  abstract findById(id: number): Promise<T | null>;

  /**
   * Create a new record
   */
  abstract create(data: CreateInput): Promise<T>;

  /**
   * Update a record by ID
   */
  abstract update(id: number, data: UpdateInput): Promise<T>;

  /**
   * Delete a record by ID
   */
  abstract delete(id: number): Promise<boolean>;

  /**
   * Count total records
   */
  abstract count(): Promise<number>;
}
