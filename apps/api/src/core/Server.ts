import express, { Express } from 'express';
import { json, urlencoded } from 'body-parser';
import morgan from 'morgan';
import cors from 'cors';
import { useExpressServer } from 'routing-controllers';
import 'reflect-metadata';

/**
 * Express server wrapper that integrates with routing-controllers
 * Provides automatic controller registration and middleware setup
 */
export class Server {
  private app: Express;
  private port: number;

  constructor(port: number = 5001) {
    this.port = port;
    this.app = express();
    this.setupMiddleware();
  }

  /**
   * Setup basic Express middleware
   */
  private setupMiddleware(): void {
    this.app
      .disable('x-powered-by')
      .use(morgan('dev'))
      .use(urlencoded({ extended: true }))
      .use(json())
      .use(cors());
  }

  /**
   * Register controllers with routing-controllers
   * @param controllers Array of controller classes
   */
  public registerControllers(controllers: Function[]): void {
    useExpressServer(this.app, {
      controllers,
      // Enable validation using class-validator
      validation: true,
      // Return validation errors in a structured format
      defaultErrorHandler: false,
    });
  }

  /**
   * Start the server
   */
  public listen(callback?: () => void): void {
    this.app.listen(this.port, callback);
  }

  /**
   * Get the Express app instance (useful for testing)
   */
  public getApp(): Express {
    return this.app;
  }

  /**
   * Get the port number
   */
  public getPort(): number {
    return this.port;
  }
}
