import { z } from "zod";
import { createSchema, createDocument } from "zod-openapi";
import type { OpenAPIObject, SchemaObject, ReferenceObject } from "openapi3-ts/oas31";
import { SignInSchema } from "@repo/validation";

// ====================
// Zod Schemas with .meta()
// ====================

// Sign-in request
export const SignInRequestSchema = SignInSchema.meta({
  description: "Sign-in request body schema",
});

// Sign-in response
export const SignInResponseSchema = z
  .object({
    accessToken: z.string().meta({ description: "JWT access token" }),
    refreshToken: z.string().meta({ description: "JWT refresh token" }),
  })
  .meta({ description: "Response returned after successful sign-in" });

// Health check response
export const HealthCheckResponseSchema = z
  .object({
    status: z.enum(["ok"]).meta({ description: "Health status" }),
  })
  .meta({ description: "Health check response" });

// ====================
// OpenAPI Components
// ====================

// Explicitly type all schema components to avoid TS2742
export const SignInRequestComponent: SchemaObject | ReferenceObject = createSchema(
  SignInRequestSchema,
  {
    io: "input",
  }
).schema;

export const SignInResponseComponent: SchemaObject | ReferenceObject = createSchema(
  SignInResponseSchema,
  {
    io: "output",
  }
).schema;

export const HealthCheckResponseComponent: SchemaObject | ReferenceObject = createSchema(
  HealthCheckResponseSchema,
  {
    io: "output",
  }
).schema;

// ====================
// OpenAPI Document
// ====================

// Explicitly typed as OpenAPIObject to prevent TS2742
export const authApiDocument: OpenAPIObject = createDocument({
  openapi: "3.0.0",
  info: {
    version: "1.0.0",
    title: "Auth API",
    description: "Authentication and Health endpoints for the Cadet Labs application",
  },
  servers: [{ url: "http://localhost:3005", description: "Development Server" }],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
      cookieAuth: {
        type: "apiKey",
        in: "cookie",
        name: "sAccessToken",
      },
    },
    schemas: {
      SignInRequest: SignInRequestComponent,
      SignInResponse: SignInResponseComponent,
      HealthCheckResponse: HealthCheckResponseComponent,
    },
  },
  paths: {
    "/auth/signin": {
      post: {
        tags: ["Auth"],
        summary: "Sign in user",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: { $ref: "#/components/schemas/SignInRequest" },
            },
          },
        },
        responses: {
          "200": {
            description: "Successful sign-in",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/SignInResponse" },
              },
            },
          },
          "401": {
            description: "Invalid credentials",
          },
        },
      },
    },
    "/auth/me": {
      get: {
        tags: ["Auth"],
        summary: "Get current user information",
        security: [{ bearerAuth: [] }, { cookieAuth: [] }],
        responses: {
          "200": {
            description: "User information retrieved successfully",
            content: {
              "application/json": {
                // schema: { $ref: "#/components/schemas/UserInfoResponse" },
              },
            },
          },
          "401": {
            description: "Unauthorized - user not authenticated",
          },
        },
      },
    },
    "/health": {
      get: {
        tags: ["Health"],
        summary: "Health check",
        responses: {
          "200": {
            description: "Service is healthy",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/HealthCheckResponse" },
              },
            },
          },
        },
      },
    },
  },
});
