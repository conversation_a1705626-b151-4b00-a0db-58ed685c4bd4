import EmailPassword from "supertokens-node/recipe/emailpassword";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import EmailVerification from "supertokens-node/recipe/emailverification";
import { listUsersByAccountInfo } from "supertokens-node";
import { logInfo, logError, logWarn } from "@repo/logger";
import { Request, Response } from "express";
import {
  SignInInput,
  SignUpInput,
  AuthResponse,
  UserMetadata as UserMetadataType,
  UpdateUserMetadataInput,
  parseFormFields,
} from "@repo/validation";

export class AuthService {
  /**
   * Sign in user with email and password
   */
  async signIn(data: SignInInput, req: Request, res: Response): Promise<AuthResponse> {
    try {
      const fields = parseFormFields(data.formFields);
      const { email, password } = fields;

      logInfo("User sign in attempt", { email });

      const response = await EmailPassword.signIn("public", email, password);

      if (response.status === "OK") {
        const recipeUserId = response.recipeUserId;
        const user = response.user;

        // Create new session
        await Session.createNewSession(req, res, "public", recipeUserId, {}, {}, {});

        // Get user metadata
        const metadata = await UserMetadata.getUserMetadata(user.id);

        logInfo("User signed in successfully", { userId: user.id, email });

        return {
          status: "OK",
          user: {
            id: user.id,
            email: user.emails[0],
            timeJoined: user.timeJoined,
            metadata: metadata.metadata as UserMetadataType,
          },
        };
      } else if (response.status === "WRONG_CREDENTIALS_ERROR") {
        logWarn("Sign in failed - wrong credentials", { email });
        return { status: "WRONG_CREDENTIALS_ERROR" };
      }

      return { status: "UNKNOWN_ERROR" };
    } catch (error) {
      logError("Sign in error", error as Error);
      throw new Error("Sign in failed");
    }
  }

  /**
   * Sign up new user
   */
  async signUp(data: SignUpInput): Promise<AuthResponse> {
    try {
      logInfo("SignUp data received in service", {
        data,
        formFields: data.formFields,
        dataKeys: Object.keys(data),
        formFieldsType: typeof data.formFields,
        formFieldsLength: data.formFields?.length,
      });

      // Direct extraction instead of using parseFormFields for debugging
      const emailField = data.formFields?.find((f) => f.id === "email");
      const passwordField = data.formFields?.find((f) => f.id === "password");
      const email = emailField?.value || "";
      const password = passwordField?.value || "";

      logInfo("User sign up attempt", {
        email,
        password: password ? "[REDACTED]" : "MISSING",
      });

      const response = await EmailPassword.signUp("public", email, password);

      if (response.status === "OK") {
        const user = response.user;

        // Set default metadata
        await UserMetadata.updateUserMetadata(user.id, {
          isActive: true,
          role: "user",
        });

        logInfo("User signed up successfully", { userId: user.id, email });

        return {
          status: "OK",
          user: {
            id: user.id,
            email: user.emails[0],
            timeJoined: user.timeJoined,
          },
        };
      } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
        logWarn("Sign up failed - email already exists", { email });
        return { status: "EMAIL_ALREADY_EXISTS_ERROR" };
      }

      return { status: "UNKNOWN_ERROR" };
    } catch (error) {
      logError("Sign up error", error as Error);
      throw new Error("Sign up failed");
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(userId: string): Promise<any> {
    try {
      const metadata = await UserMetadata.getUserMetadata(userId);

      return metadata;
    } catch (error) {
      logError("Get current user error", error as Error);
      throw new Error("Failed to get user information");
    }
  }

  /**
   * Update user metadata
   */
  async updateUserMetadata(userId: string, metadata: UpdateUserMetadataInput): Promise<void> {
    try {
      await UserMetadata.updateUserMetadata(userId, metadata);
      logInfo("User metadata updated", { userId, metadata });
    } catch (error) {
      logError("Update user metadata error", error as Error);
      throw new Error("Failed to update user metadata");
    }
  }

  /**
   * Clear user metadata
   */
  async clearUserMetadata(userId: string): Promise<void> {
    try {
      await UserMetadata.clearUserMetadata(userId);
      logInfo("User metadata cleared", { userId });
    } catch (error) {
      logError("Clear user metadata error", error as Error);
      throw new Error("Failed to clear user metadata");
    }
  }

  /**
   * Check if email exists
   */
  async checkEmailExists(email: string): Promise<{ exists: boolean }> {
    try {
      const users = await listUsersByAccountInfo("public", {
        email,
      });

      return { exists: users.length > 0 };
    } catch (error) {
      logError("Check email exists error", error as Error);
      throw new Error("Failed to check email");
    }
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(userId: string): Promise<{ status: string }> {
    try {
      await EmailVerification.sendEmailVerificationEmail("public", userId);
      logInfo("Email verification sent", { userId });
      return { status: "OK" };
    } catch (error) {
      logError("Send email verification error", error as Error);
      throw new Error("Failed to send email verification");
    }
  }

  /**
   * Verify email token
   */
  async verifyEmailToken(token: string): Promise<{ status: string }> {
    try {
      const response = await EmailVerification.verifyEmailUsingToken("public", token);

      if (response.status === "OK") {
        logInfo("Email verified successfully", { userId: response.user.id });
        return { status: "OK" };
      }

      return { status: response.status };
    } catch (error) {
      logError("Verify email token error", error as Error);
      throw new Error("Failed to verify email");
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string): Promise<{ status: string }> {
    try {
      const response = await EmailPassword.sendPasswordResetEmail("public", email);

      if (response.status === "OK") {
        logInfo("Password reset email sent", { email });
        return { status: "OK" };
      }

      return { status: response.status };
    } catch (error) {
      logError("Send password reset email error", error as Error);
      throw new Error("Failed to send password reset email");
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<{ status: string }> {
    try {
      const response = await EmailPassword.resetPasswordUsingToken("public", token, newPassword);

      if (response.status === "OK") {
        logInfo("Password reset successfully", { userId: response.userId });
        return { status: "OK" };
      }

      return { status: response.status };
    } catch (error) {
      logError("Reset password error", error as Error);
      throw new Error("Failed to reset password");
    }
  }
}
