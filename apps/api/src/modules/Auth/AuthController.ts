import type { Response, Request } from "express";
import type { SessionRequest } from "supertokens-node/framework/express";

import {
  JsonController,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  QueryParam,
  Res,
  Req,
  UseBefore,
} from "../../core/decorators";
import { AuthService } from "./AuthService";
import {
  SignInSchema,
  SignUpSchema,
  EmailSchema,
  PasswordResetSchema,
  UpdateUserMetadataSchema,
  EmailVerificationSchema,
  type SignInInput,
  type SignUpInput,
  type EmailInput,
  type PasswordResetInput,
  type UpdateUserMetadataInput,
  type EmailVerificationInput,
} from "@repo/validation";
import { validateBody, validateData } from "../../core/utils/validate";
import { requireAuth, withUserMetadata } from "../../core/middleware/SuperTokensMiddleware";

@JsonController("/auth")
export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // @Post("/signin")
  // async signIn(@Body({ validate: true }) body: SignInInput, @Req() req: any, @Res() res: Response) {
  //   const validatedData = validateData(SignInSchema, body);

  //   if (!validatedData) {
  //     return res.status(400).json({ error: "Invalid request body" });
  //   }
  //   const result = await this.authService.signIn(body, req, res);

  //   return res.json(result);
  // }

  /**
   * Health check endpoint
   */
  @Get("/health")
  async healthCheck(@Res() res: Response) {
    return res.json({
      success: true,
      message: "Auth service is healthy",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get current user information
   */
  @Get("/me")
  @UseBefore(requireAuth(), withUserMetadata)
  async getCurrentUser(@Req() req: SessionRequest, @Res() res: Response) {
    const userId = req.session!.getUserId();
    const user = await this.authService.getCurrentUser(userId);
    return res.json(user);
  }

  /**
   * Update user metadata
   */
  @Put("/me/metadata")
  @UseBefore(requireAuth())
  async updateUserMetadata(
    @Body() body: UpdateUserMetadataInput,
    @Req() req: SessionRequest,
    @Res() res: Response
  ) {
    const validatedData = validateData(UpdateUserMetadataSchema, body);
    const userId = req.session!.getUserId();
    await this.authService.updateUserMetadata(userId, validatedData);
    return res.json({
      success: true,
      message: "Metadata updated successfully",
    });
  }

  /**
   * Clear user metadata
   */
  @Delete("/me/metadata")
  @UseBefore(requireAuth())
  async clearUserMetadata(@Req() req: any, @Res() res: Response) {
    const userId = req.session.getUserId();
    await this.authService.clearUserMetadata(userId);
    return res.json({
      success: true,
      message: "Metadata cleared successfully",
    });
  }

  /**
   * Check if email exists
   */
  @Get("/email/exists")
  async checkEmailExists(@QueryParam("email") email: string, @Res() res: Response) {
    const validatedData = validateData(EmailSchema, { email });
    const result = await this.authService.checkEmailExists(validatedData.email);
    return res.json(result);
  }

  /**
   * Send email verification
   */
  @Post("/email/verify/send")
  @UseBefore(requireAuth())
  async sendEmailVerification(@Req() req: any, @Res() res: Response) {
    const userId = req.session.getUserId();
    const result = await this.authService.sendEmailVerification(userId);
    return res.json(result);
  }

  /**
   * Verify email with token
   */
  @Post("/email/verify")
  async verifyEmail(@Body() body: EmailVerificationInput, @Res() res: Response) {
    const validatedData = validateData(EmailVerificationSchema, body);
    const result = await this.authService.verifyEmailToken(validatedData.token);
    return res.json(result);
  }

  /**
   * Send password reset email
   */
  @Post("/password/reset/send")
  async sendPasswordResetEmail(@Body() body: EmailInput, @Res() res: Response) {
    const validatedData = validateData(EmailSchema, body);
    const result = await this.authService.sendPasswordResetEmail(validatedData.email);
    return res.json(result);
  }

  /**
   * Reset password with token
   */
  @Post("/password/reset")
  async resetPassword(@Body() body: { token: string; newPassword: string }, @Res() res: Response) {
    const result = await this.authService.resetPassword(body.token, body.newPassword);
    return res.json(result);
  }
}
