import { z } from 'zod';

/**
 * Schema for creating a new user
 * Validates input data when creating users
 */
export const CreateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters long')
});

/**
 * Schema for user response data
 * Shapes the output by excluding sensitive fields like password
 */
export const UserResponseSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string()
});

/**
 * Schema for updating user data
 * All fields are optional for partial updates
 */
export const UpdateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
  password: z.string().min(6).optional()
});

/**
 * Schema for user ID parameter validation
 */
export const UserParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10)).refine(val => !isNaN(val), {
    message: 'ID must be a valid number'
  })
});

// TypeScript types inferred from Zod schemas
export type CreateUserInput = z.infer<typeof CreateUserSchema>;
export type UserResponse = z.infer<typeof UserResponseSchema>;
export type UpdateUserInput = z.infer<typeof UpdateUserSchema>;
export type UserParams = z.infer<typeof UserParamsSchema>;

// Internal user type (includes password for storage)
export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  createdAt: Date;
  updatedAt: Date;
}
