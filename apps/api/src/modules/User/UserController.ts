import type { Response } from "express";
import {
  JsonController,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Res,
  UseBefore,
} from "../../core/decorators";
import { UserService } from "./UserService";
import {
  CreateUserSchema,
  UserResponseSchema,
  UpdateUserSchema,
  UserParamsSchema,
  type CreateUserInput,
  type UpdateUserInput,
} from "./user.schema";
import {
  validateBody,
  validateParams,
  shapeResponse,
  shapeArrayResponse,
} from "@/core/utils/validate";
import { BaseResponse, ErrorResponse, ResponseHelper } from "@/core/Responses";

/**
 * UserController handles HTTP requests for user operations
 * Uses decorators for routing and automatic middleware attachment
 */
@JsonController("/users")
export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * GET /users - Get all users
   * Returns all users with password field excluded
   */
  @Get("/")
  public async getUsers(@Res() res: Response): Promise<Response> {
    try {
      const users = await this.userService.getUsers();

      // Shape response to exclude password and other sensitive fields
      const shapedUsers = shapeArrayResponse(users, UserResponseSchema);

      return res.json({
        success: true,
        data: shapedUsers,
        count: shapedUsers.length,
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: "Failed to fetch users",
        message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * GET /users/:id - Get user by ID
   * Returns a single user with password field excluded
   */
  @Get("/:id")
  @UseBefore(validateParams(UserParamsSchema))
  public async getUserById(@Param("id") id: string, @Res() res: Response): Promise<Response> {
    return ResponseHelper.handleAsync(
      res,
      async () => {
        const userId = parseInt(id, 10);
        const user = await this.userService.getUserById(userId);

        if (!user) {
          throw new Error("User not found");
        }

        // Shape response to exclude password
        return shapeResponse(user, UserResponseSchema);
      },
      "User retrieved successfully"
    );
  }

  /**
   * POST /users - Create a new user
   * Validates input with CreateUserSchema and returns shaped response
   */
  @Post("/")
  @UseBefore(validateBody(CreateUserSchema))
  public async createUser(
    @Body() userData: CreateUserInput,
    @Res() res: Response
  ): Promise<Response> {
    try {
      const newUser = await this.userService.createUser(userData);

      // Shape response to exclude password
      const shapedUser = shapeResponse(newUser, UserResponseSchema);

      return res.status(201).json({
        success: true,
        data: shapedUser,
        message: "User created successfully",
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("already exists")) {
        return res.status(409).json({
          success: false,
          error: "Conflict",
          message: error.message,
        });
      }

      return res.status(500).json({
        success: false,
        error: "Failed to create user",
        message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * PUT /users/:id - Update an existing user
   * Validates input with UpdateUserSchema and returns shaped response
   */
  @Put("/:id")
  @UseBefore(validateParams(UserParamsSchema))
  @UseBefore(validateBody(UpdateUserSchema))
  public async updateUser(
    @Param("id") id: string,
    @Body() updateData: UpdateUserInput,
    @Res() res: Response
  ): Promise<Response> {
    try {
      const userId = parseInt(id, 10);
      const updatedUser = await this.userService.updateUser(userId, updateData);

      // Shape response to exclude password
      const shapedUser = shapeResponse(updatedUser, UserResponseSchema);

      return res.json({
        success: true,
        data: shapedUser,
        message: "User updated successfully",
      });
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === "User not found") {
          return res.status(404).json({
            success: false,
            error: "User not found",
          });
        }
        if (error.message.includes("already exists")) {
          return res.status(409).json({
            success: false,
            error: "Conflict",
            message: error.message,
          });
        }
      }

      return res.status(500).json({
        success: false,
        error: "Failed to update user",
        message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * DELETE /users/:id - Delete a user
   */
  @Delete("/:id")
  @UseBefore(validateParams(UserParamsSchema))
  public async deleteUser(@Param("id") id: string, @Res() res: Response): Promise<Response> {
    try {
      const userId = parseInt(id, 10);
      const deleted = await this.userService.deleteUser(userId);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }

      return res.json({
        success: true,
        message: "User deleted successfully",
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: "Failed to delete user",
        message: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
}
