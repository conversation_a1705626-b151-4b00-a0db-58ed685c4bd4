import { User, CreateUserInput, UpdateUserInput } from "./user.schema";
import { UserRepository } from "./UserRepository";

/**
 * UserService handles all user-related business logic
 * Now uses Prisma database through UserRepository
 */
export class UserService {
  private userRepository: UserRepository;

  constructor() {
    this.userRepository = new UserRepository();
  }

  /**
   * Get all users
   * @returns Array of all users
   */
  public async getUsers(): Promise<User[]> {
    return this.userRepository.findAll();
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns User if found, null otherwise
   */
  public async getUserById(id: number): Promise<User | null> {
    return this.userRepository.findById(id);
  }

  /**
   * Get a user by email
   * @param email User email
   * @returns User if found, null otherwise
   */
  public async getUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findByEmail(email);
  }

  /**
   * Create a new user
   * @param userData User data from request
   * @returns Created user
   * @throws Error if email already exists
   */
  public async createUser(userData: CreateUserInput): Promise<User> {
    // Check if email already exists
    const existingUser = await this.getUserByEmail(userData.email);
    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    return this.userRepository.create(userData);
  }

  /**
   * Update an existing user
   * @param id User ID
   * @param updateData Partial user data to update
   * @returns Updated user
   * @throws Error if user not found or email already exists
   */
  public async updateUser(
    id: number,
    updateData: UpdateUserInput
  ): Promise<User> {
    const user = await this.getUserById(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if email is being updated and already exists
    if (updateData.email && updateData.email !== user.email) {
      const existingUser = await this.getUserByEmail(updateData.email);
      if (existingUser) {
        throw new Error("User with this email already exists");
      }
    }

    return this.userRepository.update(id, updateData);
  }

  /**
   * Delete a user
   * @param id User ID
   * @returns true if deleted, false if not found
   */
  public async deleteUser(id: number): Promise<boolean> {
    return this.userRepository.delete(id);
  }

  /**
   * Get total user count
   * @returns Number of users
   */
  public async getUserCount(): Promise<number> {
    return this.userRepository.count();
  }

  /**
   * Get users with their posts
   * @returns Array of users with posts
   */
  public async getUsersWithPosts(): Promise<any[]> {
    return this.userRepository.findAllWithPosts();
  }

  /**
   * Get user by ID with their posts
   * @param id User ID
   * @returns User with posts if found, null otherwise
   */
  public async getUserByIdWithPosts(id: number): Promise<any | null> {
    return this.userRepository.findByIdWithPosts(id);
  }

  /**
   * Verify user password for authentication
   * @param email User email
   * @param password Plain text password
   * @returns User if credentials are valid, null otherwise
   */
  public async verifyPassword(
    email: string,
    password: string
  ): Promise<User | null> {
    return this.userRepository.verifyPassword(email, password);
  }
}
