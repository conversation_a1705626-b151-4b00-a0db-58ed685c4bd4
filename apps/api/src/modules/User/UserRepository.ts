import { User } from "@prisma/client";
import { BaseRepository } from "../../core/BaseRepository";
import { CreateUserInput, UpdateUserInput } from "./user.schema";
import bcrypt from "bcryptjs";

/**
 * UserRepository handles all database operations for users
 * Extends BaseRepository to inherit common CRUD operations
 */
export class UserRepository extends BaseRepository<User, CreateUserInput, UpdateUserInput> {
  /**
   * Find all users
   */
  async findAll(): Promise<User[]> {
    return this.prisma.user.findMany({
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Find a user by ID
   */
  async findById(id: number): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  /**
   * Create a new user
   */
  async create(data: CreateUserInput): Promise<User> {
    // Hash the password before storing
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || "12", 10);
    const hashedPassword = await bcrypt.hash(data.password, saltRounds);

    return this.prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
      },
    });
  }

  /**
   * Update a user by ID
   */
  async update(id: number, data: UpdateUserInput): Promise<User> {
    const updateData: any = {
      name: data.name,
      email: data.email,
    };

    // Hash password if it's being updated
    if (data.password) {
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || "12", 10);
      updateData.password = await bcrypt.hash(data.password, saltRounds);
    }

    return this.prisma.user.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Delete a user by ID
   */
  async delete(id: number): Promise<boolean> {
    try {
      await this.prisma.user.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Count total users
   */
  async count(): Promise<number> {
    return this.prisma.user.count();
  }

  /**
   * Find users with their posts
   */
  async findAllWithPosts(): Promise<(User & { posts: any[] })[]> {
    return this.prisma.user.findMany({
      include: {
        posts: {
          orderBy: { createdAt: "desc" },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Find a user by ID with their posts
   */
  async findByIdWithPosts(id: number): Promise<(User & { posts: any[] }) | null> {
    return this.prisma.user.findUnique({
      where: { id },
      include: {
        posts: {
          orderBy: { createdAt: "desc" },
        },
      },
    });
  }

  /**
   * Verify user password
   */
  async verifyPassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email);
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, "sss");
    return isPasswordValid ? user : null;
  }
}
