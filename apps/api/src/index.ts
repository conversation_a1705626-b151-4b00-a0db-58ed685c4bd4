import "reflect-metadata";
import "dotenv/config";
import { logInfo, logError } from "@repo/logger";
import { Server } from "./core/Server";
import { UserController } from "./modules/User/UserController";
import { AuthController } from "./modules/Auth/AuthController";
import { initializeDatabase, DatabaseConfig, disconnectDatabase } from "@/database";
import { getSuperTokensService } from "./core/services/SuperTokensService";
import { EmailService } from "./core/services/EmailService";
import {
  superTokensMiddleware,
  superTokensErrorHandler,
} from "./core/middleware/SuperTokensMiddleware";
import { setupSwagger } from "./core/swagger";
import Session from "supertokens-node/recipe/session";
import { appConfig } from "./config";

/**
 * Main application entry point
 * Sets up the server with controllers and starts listening
 */
console.log("application config", appConfig);

const port = Number(process.env.PORT) || 5001;

// Initialize database connection and start the server
async function startServer() {
  try {
    // Initialize SuperTokens
    logInfo("Initializing SuperTokens...");
    const superTokensService = getSuperTokensService();
    superTokensService.init();

    // Initialize Email Service
    const emailConnectionString = process.env.AZURE_COMMUNICATION_CONNECTION_STRING;
    if (emailConnectionString) {
      logInfo("Initializing Email service...");
      EmailService.initialize(emailConnectionString);
    } else {
      logInfo("Email service not initialized - no connection string provided");
    }

    // Create server instance
    const server = new Server(port);

    // Register all controllers
    server.registerControllers([UserController, AuthController]);
    // Add SuperTokens middleware BEFORE registering controllers
    server.getApp().use(superTokensMiddleware());

    server.getApp().use("/revoke-all-user-sessions", async (req, res) => {
      let userId = req.body.userId;
      await Session.revokeAllSessionsForUser(userId);

      res.send("Success! All user sessions have been revoked");
    });

    // Setup Swagger documentation
    setupSwagger(server.getApp());

    // Add health check endpoint
    server.getApp().get("/health", (_req, res) => {
      res.json({
        status: "ok",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });

    if (
      !process.env.APP_POSTGRESQL_USER ||
      !process.env.APP_POSTGRESQL_PASSWORD ||
      !process.env.POSTGRESQL_HOST ||
      !process.env.POSTGRESQL_PORT ||
      !process.env.POSTGRESQL_DATABASE_NAME ||
      !process.env.APP_POSTGRESQL_TABLE_SCHEMA
    ) {
      const missingVars = [
        !process.env.APP_POSTGRESQL_USER && "APP_POSTGRESQL_USER",
        !process.env.APP_POSTGRESQL_PASSWORD && "APP_POSTGRESQL_PASSWORD",
        !process.env.POSTGRESQL_HOST && "POSTGRESQL_HOST",
        !process.env.POSTGRESQL_PORT && "POSTGRESQL_PORT",
        !process.env.POSTGRESQL_DATABASE_NAME && "POSTGRESQL_DATABASE_NAME",
        !process.env.APP_POSTGRESQL_TABLE_SCHEMA && "APP_POSTGRESQL_TABLE_SCHEMA",
      ].filter(Boolean);

      logError(`❌ Missing environment variables: ${missingVars.join(", ")}`);
      throw new Error(
        "Database connection not initialized — missing required environment variables"
      );
    }

    // Connect to database
    await initializeDatabase({
      user: process.env.APP_POSTGRESQL_USER!,
      password: process.env.APP_POSTGRESQL_PASSWORD!,
      host: process.env.POSTGRESQL_HOST!,
      port: Number(process.env.POSTGRESQL_PORT),
      database: process.env.POSTGRESQL_DATABASE_NAME!,
      schema: process.env.APP_POSTGRESQL_TABLE_SCHEMA!,
    });

    // Add SuperTokens error handler (must be after routes)
    server.getApp().use(superTokensErrorHandler());

    // Start the server
    server.listen(() => {
      logInfo(`🚀 API server running on port ${port}`);
      logInfo(`📚 Health check available at http://localhost:${port}/health`);
    });
  } catch (error) {
    logError(`❌ Failed to start server`, error as Error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  logInfo("🛑 Shutting down gracefully...");
  await disconnectDatabase();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logInfo("🛑 Shutting down gracefully...");
  await disconnectDatabase();
  process.exit(0);
});

export { startServer };

startServer();
