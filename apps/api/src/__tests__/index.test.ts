import supertest from "supertest";
import { describe, it, expect } from "@jest/globals";
import { startServer } from "../index";

describe("Server", () => {
  it("health check returns 200", async () => {
    await supertest(async () => await startServer())
      .get("/status")
      .expect(200)
      .then((res) => {
        expect(res.ok).toBe(true);
      });
  });

  it("message endpoint says hello", async () => {
    await supertest(async () => await startServer())
      .get("/message/jared")
      .expect(200)
      .then((res) => {
        expect(res.body).toEqual({ message: "hello jared" });
      });
  });
});
