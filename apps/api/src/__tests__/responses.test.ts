import { Response } from "express";
import { BaseResponse, ErrorResponse, ResponseHelper, ValidationError } from "../core/Responses";

// Mock Express Response object
const createMockResponse = () => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
  } as unknown as Response;

  return res;
};

describe("BaseResponse", () => {
  let mockRes: Response;

  beforeEach(() => {
    mockRes = createMockResponse();
  });

  describe("success", () => {
    it("should send success response with data", () => {
      const testData = { id: 1, name: "Test User" };

      BaseResponse.success(mockRes, testData, "Success message");

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "Success message",
        data: testData,
        meta: undefined,
      });
    });

    it("should send success response with custom status code", () => {
      BaseResponse.success(mockRes, null, "Custom success", 202);

      expect(mockRes.status).toHaveBeenCalledWith(202);
    });

    it("should send success response with metadata", () => {
      const meta = { page: 1, limit: 10, total: 100 };

      BaseResponse.success(mockRes, [], "Success with meta", 200, meta);

      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "Success with meta",
        data: [],
        meta,
      });
    });
  });

  describe("created", () => {
    it("should send 201 created response", () => {
      const testData = { id: 1, name: "New User" };

      BaseResponse.created(mockRes, testData, "User created");

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "User created",
        data: testData,
        meta: undefined,
      });
    });

    it("should use default message when none provided", () => {
      BaseResponse.created(mockRes, {});

      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "Resource created successfully",
        data: {},
        meta: undefined,
      });
    });
  });

  describe("noContent", () => {
    it("should send 204 no content response", () => {
      BaseResponse.noContent(mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });
  });
});

describe("ErrorResponse", () => {
  let mockRes: Response;

  beforeEach(() => {
    mockRes = createMockResponse();
  });

  describe("error", () => {
    it("should send generic error response", () => {
      ErrorResponse.error(mockRes, "Something went wrong", 500, "INTERNAL_ERROR");

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "Something went wrong",
        error: "INTERNAL_ERROR",
      });
    });
  });

  describe("badRequest", () => {
    it("should send 400 bad request response", () => {
      ErrorResponse.badRequest(mockRes, "Invalid input");

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "Invalid input",
        error: undefined,
      });
    });
  });

  describe("validationError", () => {
    it("should send validation error response with field errors", () => {
      const validationErrors: ValidationError[] = [
        { field: "email", message: "Invalid email", code: "invalid_email" },
        { field: "password", message: "Too short", code: "min_length" },
      ];

      ErrorResponse.validationError(mockRes, validationErrors, "Validation failed");

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
      });
    });
  });

  describe("notFound", () => {
    it("should send 404 not found response", () => {
      ErrorResponse.notFound(mockRes, "User not found");

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "User not found",
        error: undefined,
      });
    });
  });

  describe("conflict", () => {
    it("should send 409 conflict response", () => {
      ErrorResponse.conflict(mockRes, "Email already exists");

      expect(mockRes.status).toHaveBeenCalledWith(409);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "Email already exists",
        error: undefined,
      });
    });
  });
});

describe("ResponseHelper", () => {
  let mockRes: Response;

  beforeEach(() => {
    mockRes = createMockResponse();
  });

  describe("handleAsync", () => {
    it("should handle successful async operation", async () => {
      const testData = { id: 1, name: "Test" };
      const operation = jest.fn().mockResolvedValue(testData);

      await ResponseHelper.handleAsync(mockRes, operation, "Success");

      expect(operation).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "Success",
        data: testData,
        meta: undefined,
      });
    });

    it("should handle 'not found' error", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("User not found"));

      await ResponseHelper.handleAsync(mockRes, operation);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "User not found",
        error: undefined,
      });
    });

    it("should handle 'already exists' error", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Email already exists"));

      await ResponseHelper.handleAsync(mockRes, operation);

      expect(mockRes.status).toHaveBeenCalledWith(409);
    });

    it("should handle validation error", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Validation failed"));

      await ResponseHelper.handleAsync(mockRes, operation);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should handle generic error", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Database error"));

      await ResponseHelper.handleAsync(mockRes, operation);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "An unexpected error occurred",
        error: "Database error",
      });
    });
  });

  describe("paginated", () => {
    it("should create paginated response with metadata", () => {
      const data = [{ id: 1 }, { id: 2 }];

      ResponseHelper.paginated(mockRes, data, 1, 10, 25, "Users retrieved");

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: "Users retrieved",
        data,
        meta: {
          page: 1,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrev: false,
        },
      });
    });

    it("should calculate pagination metadata correctly", () => {
      ResponseHelper.paginated(mockRes, [], 2, 5, 12);

      const callArgs = (mockRes.json as jest.Mock).mock.calls[0][0];
      expect(callArgs.meta).toEqual({
        page: 2,
        limit: 5,
        total: 12,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });
    });
  });
});
