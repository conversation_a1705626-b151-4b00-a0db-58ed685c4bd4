import { PrismaClient } from '@prisma/client';
import { appConfig } from '../config';

export const prisma = new PrismaClient({
  datasources: {
    db: { url: appConfig.database.url },
  },
  log: ['query', 'info', 'warn', 'error'],
});

export const initDB = async () => {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
  } catch (err) {
    console.error('❌ Failed to connect to database:', err);
    throw err;
  }
};

export const closeDB = async () => {
  await prisma.$disconnect();
  console.log('🛑 Database connection closed');
};