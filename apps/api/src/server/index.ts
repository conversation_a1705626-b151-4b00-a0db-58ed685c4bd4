import express, { Express } from 'express';
import { json, urlencoded } from 'body-parser';
import { requestLoggingMiddleware } from './request.logger';
import cors from 'cors';
import { useExpressServer } from 'routing-controllers';
import 'reflect-metadata';
import { AuthController } from '@/modules/Auth/AuthController';
import { UserController } from '@/modules/User/UserController';
import { appConfig } from "../config";
import { initDB, closeDB } from './db.client';
import { getAuthProviderErrorHandler, getAuthProviderMiddleware, initAuthProvider } from './auth.provider';



export const bootstrapServer = async () => {

    await initDB();
    initAuthProvider();

const app = express();
app
      .disable('x-powered-by')
      .use(requestLoggingMiddleware())
      .use(urlencoded({ extended: true }))
      .use(json())
      .use(cors());


      app.use(getAuthProviderMiddleware());
      app.use(getAuthProviderErrorHandler());

   useExpressServer(app, {
      controllers: [AuthController, UserController],
      // Enable validation using class-validator
      validation: true,
      // Return validation errors in a structured format
      defaultErrorHandler: false,
    });

  // Start server
  const server = app.listen(appConfig.server.port, () => {
    console.log(`🚀 Server running on port ${appConfig.server.port}`);
  });

  // Graceful shutdown
  const shutdown = () => {
    console.log('⚡ Shutting down server...');
    server.close(async () => {
      await closeDB();
      process.exit(0);
    });
  };
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
};