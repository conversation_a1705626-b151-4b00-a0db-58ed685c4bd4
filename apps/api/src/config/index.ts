import 'reflect-metadata';
import { Transform, Type } from 'class-transformer';
import { IsString, IsN<PERSON>ber, IsUrl, IsEnum, IsO<PERSON>al, Min, Max, validateSync } from 'class-validator';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export class DatabaseConfig {
  @IsString()
  user!: string;

  @IsString()
  password!: string;

  @IsString()
  host!: string;

  @IsNumber()
  @Min(1)
  @Max(65535)
  @Transform(({ value }) => parseInt(value, 10))
  port!: number;

  @IsString()
  database!: string;

  @IsString()
  schema!: string;

  @IsOptional()
  @IsUrl()
  url?: string;
}

export class EmailConfig {
  @IsString()
  azureConnectionString!: string;
}

export class AuthConfig {
  @IsUrl()
  websiteDomain!: string;

  @IsUrl()
  serverDomain!: string;
}

export class ServerConfig {
  @IsNumber()
  @Min(1)
  @Max(65535)
  @Transform(({ value }) => parseInt(value, 10))
  port!: number;

  @IsEnum(['development', 'production', 'test', 'staging'])
  nodeEnv!: 'development' | 'production' | 'test' | 'staging';
}

export class AppConfig {
  @Type(() => DatabaseConfig)
  database!: DatabaseConfig;

  @Type(() => EmailConfig)
  email!: EmailConfig;

  @Type(() => AuthConfig)
  auth!: AuthConfig;

  @Type(() => ServerConfig)
  server!: ServerConfig;

  constructor() {
    // Database configuration
    this.database = new DatabaseConfig();
    this.database.user = process.env.APP_POSTGRESQL_USER || '';
    this.database.password = process.env.APP_POSTGRESQL_PASSWORD || '';
    this.database.host = process.env.POSTGRESQL_HOST || '';
    this.database.port = parseInt(process.env.POSTGRESQL_PORT || '5432', 10);
    this.database.database = process.env.POSTGRESQL_DATABASE_NAME || '';
    this.database.schema = process.env.APP_POSTGRESQL_TABLE_SCHEMA || 'public';
    this.database.url = process.env.DATABASE_URL;

    // Email configuration
    this.email = new EmailConfig();
    this.email.azureConnectionString = process.env.AZURE_COMMUNICATION_CONNECTION_STRING || '';

    // Auth configuration
    this.auth = new AuthConfig();
    this.auth.websiteDomain = process.env.WEBSITE_DOMAIN || '';
    this.auth.serverDomain = process.env.SERVER_DOMAIN || '';

    // Server configuration
    this.server = new ServerConfig();
    this.server.port = parseInt(process.env.PORT || '3000', 10);
    this.server.nodeEnv = (process.env.NODE_ENV as ServerConfig['nodeEnv']) || 'development';

    // Validate the configuration
    this.validate();
  }

  private validate(): void {
    const errors = validateSync(this, { validationError: { target: false } });
    if (errors.length > 0) {
      const errorMessages = errors.map(error =>
        Object.values(error.constraints || {}).join(', ')
      ).join('; ');
      throw new Error(`Configuration validation failed: ${errorMessages}`);
    }
  }
}

// Export singleton instance
export const appConfig = new AppConfig();