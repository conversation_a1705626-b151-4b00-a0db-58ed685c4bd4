// prisma/extensions/tenant.ts
import { Prisma } from "@prisma/client";

export function tenantExtension(tenantId: string) {
  return Prisma.defineExtension((client) =>
    client.$extends({
      query: {
        $allModels: {
          async $allOperations({ args, query }) {
            // Set current_tenant in the DB session before each query
            await client.$executeRawUnsafe(`SET app.current_tenant = '${tenantId}'`);
            // Execute the original query
            return query(args);
          },
        },
      },
    })
  );
}
