"use client";

import {
  useQuery,
  useInfiniteQuery,
  UseQueryOptions,
  UseQueryResult,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  QueryKey,
  QueryClient,
} from "@tanstack/react-query";
import { useTenant } from "@/lib/tenant-context";

/**
 * A wrapper around useQuery that automatically includes the current tenant ID in the query key.
 * Ensures that queries are properly cached per tenant and invalidated when tenant changes.
 */
export function useTenantAwareQuery<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, "queryKey"> & {
    queryKey: TQueryKey;
  }
): UseQueryResult<TData, TError> {
  const { currentTenantId } = useTenant();

  const tenantAwareQueryKey = [currentTenantId, ...options.queryKey] as unknown as TQ<PERSON><PERSON><PERSON><PERSON>;

  return useQuery({
    ...options,
    queryKey: tenantAwareQueryKey,
  });
}

/**
 * A wrapper around useInfiniteQuery that automatically includes the current tenant ID in the query key.
 * Ensures that infinite queries are properly cached per tenant and invalidated when tenant changes.
 */
export function useTenantAwareInfiniteQuery<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
  TPageParam = unknown,
>(
  options: Omit<
    UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,
    "queryKey"
  > & { queryKey: TQueryKey }
): UseInfiniteQueryResult<TData, TError> {
  const { currentTenantId } = useTenant();

  const tenantAwareQueryKey = [currentTenantId, ...options.queryKey] as unknown as TQueryKey;

  return useInfiniteQuery({
    ...options,
    queryKey: tenantAwareQueryKey,
  });
}

/**
 * Helper hook to get a tenant-aware query key.
 * Useful when you need to manually invalidate queries.
 */
export function useTenantAwareQueryKey(baseKey: QueryKey): readonly unknown[] {
  const { currentTenantId } = useTenant();
  return [currentTenantId, ...baseKey] as const;
}

/**
 * Hook to invalidate all queries for the current tenant.
 */
export function useInvalidateTenantQueries(): (queryClient: QueryClient) => void {
  const { currentTenantId } = useTenant();

  return (queryClient: QueryClient) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        return query.queryKey?.[0] === currentTenantId;
      },
    });
  };
}
