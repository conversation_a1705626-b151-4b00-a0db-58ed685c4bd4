import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { Navbar } from "@/components/nav-bar";
import { SuperTokenSessionAuthWrapper } from "@/components/auth";
import { QueryProvider } from "@/components/api-query";
import { cn } from "@repo/ui/lib/utils";
import { MetaDataProvider } from "@/components/meta-data-provider";
import { AccessProvider } from "@/components/access-provider";
import { TenantProvider } from "@/lib/tenant-context";

const geist = Geist({ subsets: ["latin"] });

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  return (
    <SuperTokenSessionAuthWrapper>
      <QueryProvider>
        <MetaDataProvider>
          <AccessProvider>
            <TenantProvider>
              <div className={cn(geist.className, "flex h-full flex-col")}>
                <Navbar />
                <main className="flex-1 bg-[#f0f7f6] p-8">{children}</main>
              </div>
            </TenantProvider>
          </AccessProvider>
        </MetaDataProvider>
      </QueryProvider>
    </SuperTokenSessionAuthWrapper>
  );
}
