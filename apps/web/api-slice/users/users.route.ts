import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import instance from "../lib/instance";
import { useTenantAwareInfiniteQuery } from "@/hooks/use-tenant-aware-query";
import { useTenant } from "@/lib/tenant-context";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  status: "pending_verification" | "active" | "inactive";
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

export type CreateUserPayload = {
  firstName: string;
  lastName: string;
  emailId: string;
  role: string;
};

type EditUserPayload = {
  userId: string;
  data: CreateUserPayload;
};

type ActivateDeactivate = {
  userId: string;
  data: {
    isActive: boolean;
  };
};

type GetUsersResponse = {
  users: User[];
  nextCursor?: string;
};

// GET request with useQuery
export function useUserData(userId: string) {
  return useQuery({
    queryKey: ["users", userId],
    queryFn: async () => {
      const response = await instance.get(`/users/${userId}`);
      return response.data;
    },
  });
}

export function useGetUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await instance.get(`/users`);
      console.log("useGetUsers query", response);
      return response.data;
    },
  });
}

// export function useGetUsersList(limit = 10) {
//   return useInfiniteQuery({
//     queryKey: ["users", "list"],
//     initialPageParam: undefined as string | undefined,
//     queryFn: async ({ pageParam }: { pageParam?: string | undefined }) => {
//       const response = await instance.post<GetUsersResponse>(`/users/list`, {
//         limit,
//         cursor: pageParam,
//       });
//       return response.data;
//     },
//     getNextPageParam: (lastPage) => lastPage.nextCursor ?? undefined,
//   });
// }

export function useGetUsersList(limit = 10) {
  return useTenantAwareInfiniteQuery({
    queryKey: ["users", "list", limit],
    initialPageParam: undefined as string | undefined,
    queryFn: async ({ pageParam }: { pageParam?: string | undefined }) => {
      const response = await instance.post<GetUsersResponse>(`/users/list`, {
        limit,
        cursor: pageParam,
      });
      return response.data;
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor ?? undefined,
  });
}

// POST request with useMutation
export function useCreateUser() {
  const queryClient = useQueryClient();
  const { currentTenantId } = useTenant();
  return useMutation({
    mutationFn: async (userData: CreateUserPayload) => {
      const response = await instance.post("/users", userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch users queries
      queryClient.invalidateQueries({
        queryKey: [currentTenantId, "users", "list"],
      });
    },
  });
}

export function useEditUser() {
  const queryClient = useQueryClient();
  const { currentTenantId } = useTenant();
  return useMutation({
    mutationFn: async ({ userId, data }: EditUserPayload) => {
      const response = await instance.put(`/users/${userId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [currentTenantId, "users", "list"],
      });
    },
  });
}

// ACTIVATE user
export function useActivateUser() {
  const queryClient = useQueryClient();
  const { currentTenantId } = useTenant();
  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.put(`/users/${userId}/activate`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [currentTenantId, "users", "list"],
      });
    },
  });
}

// DEACTIVATE user
export function useDeactivateUser() {
  const queryClient = useQueryClient();
  const { currentTenantId } = useTenant();
  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.put(`/users/${userId}/deactivate`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [currentTenantId, "users", "list"],
      });
    },
  });
}

// DELETE user
export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.delete(`/users/${userId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// RESET PASSWORD User

export function useResetPasswordUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.patch(`/users/${userId}/reset-password`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// EMAIL VERIFICATION User

export function useResendEmailVerification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.post(`/users/${userId}/resend-email-verification`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}
