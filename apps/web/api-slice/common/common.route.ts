import { useQuery } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import Session from "supertokens-auth-react/recipe/session";
import instance, { authinstance } from "../lib/instance";

// GET request with useQuery
export function useGetMetaData() {
  return useQuery({
    queryKey: ["me"], // key used for caching
    queryFn: async () => {
      const response = await authinstance.get("/me");
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes: data considered fresh

    refetchOnWindowFocus: true,
  });
}

export function useLogout() {
  const queryClient = useQueryClient();

  const logout = async () => {
    // 1. Clear Supertokens session (cookies)
    await Session.signOut();

    // 2. Reset React Query cache
    queryClient.clear();

    // 3. Redirect
    window.location.href = "/login";
  };

  return logout;
}
