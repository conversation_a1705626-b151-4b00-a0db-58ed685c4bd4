import axios from "axios";

// process.env.NEXT_PUBLIC_BASE_URL

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3005/api",
  headers: {
    "Content-Type": "application/json",
  },
});

const authinstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3005/auth",
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to automatically include tenantId header
instance.interceptors.request.use(
  (config) => {
    // Get current tenant ID from localStorage
    const currentTenantId = localStorage.getItem("currentTenantId");

    if (currentTenantId) {
      config.headers["x-tenant-id"] = currentTenantId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add the same interceptor to auth instance if needed
authinstance.interceptors.request.use(
  (config) => {
    const currentTenantId = localStorage.getItem("currentTenantId");

    if (currentTenantId) {
      config.headers["x-tenant-id"] = currentTenantId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export { authinstance };

export default instance;
