import { z } from "zod";

export const editUserValidation = z.object({
    firstName: z
        .string()
        .min(1, "First name is required")
        .max(50, "First name must be at most 50 characters"),
    lastName: z
        .string()
        .min(1, "Last name is required")
        .max(50, "Last name must be at most 50 characters"),
    displayName: z
        .string()
        .max(100, "Display name must be at most 100 characters")
        .optional(),
    emailId: z
        .string()
        .min(1, "Email is required")
        .email("Invalid email format"),
    role: z.enum(["admin", "project_handler"], {
        errorMap: () => ({ message: "Role is required" }),
    }),
});

export type UpdateUserPayload = z.infer<typeof editUserValidation>;
