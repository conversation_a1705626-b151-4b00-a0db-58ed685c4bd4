"use client";

import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { useEditUser } from "@/api-slice/users";
import { toast } from "sonner";
import { UpdateUserPayload, editUserValidation } from "./lib/edit-user-validation";
import { useMetaData } from "@/components/meta-data-provider";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: any;
};

type EditUserFormProps = {
  onClose: () => void;
  selectedUser: User;
};

const EditUserForm: React.FC<EditUserFormProps> = ({ onClose, selectedUser }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<UpdateUserPayload>({
    resolver: zodResolver(editUserValidation), // ✅ use Zod schema
    defaultValues: {
      firstName: selectedUser.firstName,
      lastName: selectedUser.lastName,
      displayName: selectedUser.displayName,
      emailId: selectedUser.emailId,
      role: selectedUser.role,
    },
  });

  const { metaData } = useMetaData();
  const currentUser = metaData?.user;

  const editUser = useEditUser();
  const { isPending } = editUser;

  const onSubmit = (data: UpdateUserPayload) => {
    editUser.mutate(
      { userId: selectedUser.id, data },
      {
        onSuccess: () => {
          onClose();
          toast("User updated successfully");
          console.log("sssssss");
        },
        onError: () => {
          toast.error("Something went wrong!");
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl space-y-6">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            First Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("firstName")} placeholder="First Name" className="h-10" />
          {errors.firstName && (
            <span className="text-xs text-red-500">{errors.firstName.message}</span>
          )}
        </div>

        {/* Last Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Last Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("lastName")} placeholder="Last Name" className="h-10" />
          {errors.lastName && (
            <span className="text-xs text-red-500">{errors.lastName.message}</span>
          )}
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">Display Name</label>
          <Input {...register("displayName")} placeholder="Display Name" className="h-10" />
          {errors.displayName && (
            <span className="text-xs text-red-500">{errors.displayName.message}</span>
          )}
        </div>

        {/* Email ID (Disabled) */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("emailId")} placeholder="Email ID" className="h-10" disabled />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* Role (Disabled) */}
        <div className="flex w-full flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>
          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value || ""}
                disabled={currentUser?.emailId === selectedUser?.emailId ? true : false}
              >
                <SelectTrigger className="h-10 w-full">
                  <SelectValue placeholder="Select Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="project_handler">Project Handler</SelectItem>
                </SelectContent>
              </Select>
            )}
          />
          {errors.role && <span className="text-xs text-red-500">{errors.role.message}</span>}
        </div>
      </div>

      {/* Buttons */}
      <div className="flex w-full items-center justify-between">
        <Button
          type="button"
          variant="secondary"
          className="w-full px-8 md:w-auto"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button type="submit" className="w-full px-8 md:w-auto">
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditUserForm;
