"use client";

import React from "react";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useActivateUser, useDeactivateUser } from "@/api-slice/users";
import { toast } from "sonner";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

interface ToggleUserStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | null;
}

const ToggleUserStatusDialog: React.FC<ToggleUserStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
}) => {
  const activateUser = useActivateUser();
  const deactivateUser = useDeactivateUser();

  const handleConfirm = () => {
    if (!selectedUser) return;

    if (selectedUser.status === "active") {
      deactivateUser.mutate(selectedUser.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("User de-activated successfully");
        },
        onError: () => {
          toast.error("Something went wrong!");
        },
      });
    } else {
      activateUser.mutate(selectedUser.id, {
        onSuccess: () => {
          onOpenChange(false);
          toast.success("User activated successfully");
        },
        onError: () => {
          toast.error("Something went wrong!");
        },
      });
    }
  };

  const isLoading = activateUser.isPending || deactivateUser.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <h2 className="text-center text-lg font-semibold">
          {selectedUser?.status === "active" ? "Deactivate Tenant" : "Activate Tenant"}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Notification will be sent to the registered tenant admin email.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedUser?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleUserStatusDialog;
