"use client";
import React, { useEffect } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateUser } from "@/api-slice/users";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { toast } from "sonner";
import { addUserValidation, CreateUserPayload } from "./lib/add-user-validation";

type AddUserFormProps = {
  onSuccess?: () => void;
};

const AddUserForm: React.FC<AddUserFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateUserPayload>({
    resolver: zodResolver(addUserValidation),
    defaultValues: {
      firstName: "",
      lastName: "",
      displayName: "",
      emailId: "",
      role: "",
    },
  });

  const createUser = useCreateUser();
  const { isPending } = createUser;

  const firstName = watch("firstName");
  const lastName = watch("lastName");

  useEffect(() => {
    if (firstName && lastName) {
      setValue("displayName", `${firstName || ""} ${lastName || ""}`.trim(), {
        shouldValidate: true,
      });
    }
  }, [firstName, lastName]);

  const onSubmit: SubmitHandler<CreateUserPayload> = (data) => {
    createUser.mutate(data, {
      onSuccess: () => {
        onSuccess?.();
        toast.success("User created successfully");
      },
      onError: () => {
        toast.error("Something went wrong!");
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl space-y-6 p-6">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            First Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("firstName")} placeholder="First Name" className="h-10" />
          {errors.firstName && (
            <span className="text-xs text-red-500">{errors.firstName.message}</span>
          )}
        </div>

        {/* Last Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Last Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("lastName")} placeholder="Last Name" className="h-10" />
          {errors.lastName && (
            <span className="text-xs text-red-500">{errors.lastName.message}</span>
          )}
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">Display Name</label>
          <Input {...register("displayName")} placeholder="Display Name" className="h-10" />
          {errors.displayName && (
            <span className="text-xs text-red-500">{errors.displayName.message}</span>
          )}
        </div>

        {/* Email ID */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input {...register("emailId")} placeholder="Email ID" className="h-10" />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* User Role */}
        <div className="flex w-full flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>
          <Select
            onValueChange={(val) => setValue("role", val, { shouldValidate: true })}
            value={watch("role") || ""}
          >
            <SelectTrigger className="h-10 w-full">
              <SelectValue placeholder="Select User Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="project_handler">Project Handler</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && <span className="text-xs text-red-500">{errors.role.message}</span>}
        </div>
      </div>

      <Button type="submit" className="w-full px-8 md:w-auto">
        {isPending ? "Registering..." : "Register User"}
      </Button>
    </form>
  );
};

export default AddUserForm;
