"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import EditUserForm from "./edit-user-form";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: any;
};

interface EditTenantDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | null;
}

const EditUserDialog: React.FC<EditTenantDialogProps> = ({ open, onOpenChange, selectedUser }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="text-center">Edit User</DialogTitle>
        </DialogHeader>
        {selectedUser ? (
          <EditUserForm selectedUser={selectedUser} onClose={() => onOpenChange(false)} />
        ) : (
          <p className="text-muted-foreground">No user selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditUserDialog;
