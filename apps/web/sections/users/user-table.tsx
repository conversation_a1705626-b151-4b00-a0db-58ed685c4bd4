"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { getCoreRowModel } from "@tanstack/react-table";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { TableColumnHeader } from "./lib/table-config";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import React, { useState } from "react";
import EditUserDialog from "./edit-user-dialog";
import ToggleUserStatusDialog from "./toggle-user-status-dialog";
import ResetPasswordUserDialog from "./reset-password-dialog";
import EmailVerificationUserDialog from "./send-verification-mail";
import { CustomVirtualList } from "@/components/custom-virtual-list";
import { Badge } from "@repo/ui/components/badge";
import { useRouter } from "next/navigation";
import { useMetaData } from "@/components/meta-data-provider";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

interface UsersTableProps {
  userData: User[];
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
}

const UsersTable: React.FC<UsersTableProps> = ({
  userData,
  onEndReached,
  hasNextPage,
  isFetchingNextPage,
}) => {
  const [openEdit, setOpenEdit] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openToggle, setOpenToggle] = useState(false);
  const [openResetPassword, setOpenResetPassword] = useState(false);
  const [openEmailVerification, setOpeEmailVerification] = useState(false);
  const router = useRouter();

  const { metaData } = useMetaData();

  const currentUser = metaData?.user;

  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "firstName",
      header: ({ column }) => <TableColumnHeader column={column} title="First Name" />,
      cell: ({ row }) => (
        <p>
          {row?.original?.firstName || "-"}
          {currentUser?.emailId === row?.original?.emailId && (
            <span className="ml-1 text-red-500">(You)</span>
          )}
        </p>
      ),
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "lastName",
      header: ({ column }) => <TableColumnHeader column={column} title="Last Name" />,
      cell: ({ row }) => row?.original?.lastName || "-",
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "displayName",
      header: ({ column }) => <TableColumnHeader column={column} title="Display Name" />,
      cell: ({ row }) => row?.original?.displayName || "-",
      enableSorting: false,
      size: 300,
    },
    {
      accessorKey: "emailId",
      header: ({ column }) => <TableColumnHeader column={column} title="Email ID" />,
      cell: ({ row }) => row?.original?.emailId || "-",
      enableSorting: false,
      size: 250,
    },
    {
      accessorKey: "role",
      header: ({ column }) => <TableColumnHeader column={column} title="Role" />,
      cell: ({ row }) => row?.original?.role || "-",
      enableSorting: false,
    },
    {
      accessorKey: "status",
      header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
      size: 200,
      cell: ({ row }) => {
        const status = row.original?.status;

        // Determine color based on status
        const bgColor =
          status === "pending_verification"
            ? "bg-yellow-500"
            : status === "active"
              ? "bg-green-600"
              : "bg-red-600"; // inactive or any other fallback

        return (
          <Badge className={bgColor} color="">
            <span className="text-white">{status}</span>
          </Badge>
        );
      },
      enableSorting: false,
    },

    {
      id: "actions",
      header: "Action",

      cell: ({ row }) => {
        const user = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild disabled={user?.role === "superadmin"}>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <Icons.moreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {user?.status !== "pending_verification" && (
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedUser(user);
                    setOpenEdit(true);
                  }}
                >
                  Edit
                </DropdownMenuItem>
              )}
              {user?.status === "pending_verification" && (
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedUser(user);
                    setOpeEmailVerification(true);
                  }}
                >
                  Send verification mail
                </DropdownMenuItem>
              )}
              {currentUser?.emailId !== user?.emailId &&
                user?.status !== "pending_verification" && (
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedUser(user);
                      setOpenResetPassword(true);
                    }}
                  >
                    Reset password
                  </DropdownMenuItem>
                )}
              {/* Toggle Activate/Deactivate */}
              {currentUser?.emailId !== user?.emailId &&
                user?.status !== "pending_verification" && (
                  <DropdownMenuItem
                    onClick={() => {
                      setSelectedUser(user);
                      setOpenToggle(true);
                    }}
                  >
                    {user?.status === "active" ? "Deactivate User" : "Activate User"}
                  </DropdownMenuItem>
                )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="bg-background rounded-sm p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="p-2" onClick={() => router.back()}>
            <Icons.arrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-foreground text-2xl font-semibold">User Management</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.sortAsc className="h-4 w-4" />
            Sort
          </Button>
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Table */}

      <CustomVirtualList
        options={{
          data: userData,
          columns: columns,
          getCoreRowModel: getCoreRowModel(),
        }}
        onEndReached={onEndReached}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
      />

      {/* Edit User Dialog */}
      <EditUserDialog open={openEdit} onOpenChange={setOpenEdit} selectedUser={selectedUser} />

      <ToggleUserStatusDialog
        open={openToggle}
        onOpenChange={setOpenToggle}
        selectedUser={selectedUser}
      />

      <ResetPasswordUserDialog
        open={openResetPassword}
        onOpenChange={setOpenResetPassword}
        selectedUser={selectedUser}
      />

      <EmailVerificationUserDialog
        open={openEmailVerification}
        onOpenChange={setOpeEmailVerification}
        selectedUser={selectedUser}
      />
    </div>
  );
};

export default UsersTable;
