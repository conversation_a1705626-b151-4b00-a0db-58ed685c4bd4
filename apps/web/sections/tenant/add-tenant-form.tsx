"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateTenant } from "@/api-slice/tenant";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { addTenantValidation, CreateTenantPayload } from "./lib/add-tenant-validation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";

type AddTenantFormProps = {
  onSuccess?: () => void;
};

const AddTenantForm: React.FC<AddTenantFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CreateTenantPayload>({
    resolver: zodResolver(addTenantValidation),
    defaultValues: { capOnUsers: 1 },
  });

  const userCapacity = watch("capOnUsers");

  const createTenant = useCreateTenant();

  const { isPending } = createTenant;

  const onSubmit = (data: CreateTenantPayload) => {
    const payload = {
      ...data,
    };
    createTenant.mutate(payload, {
      onSuccess: () => {
        onSuccess?.();
        toast.success("Tenant created successfully");
      },
      onError: () => {
        toast.error("Something went wrong!");
      },
    });
  };

  // Increment / Decrement
  const increment = () => setValue("capOnUsers", (userCapacity || 0) + 1);
  const decrement = () => setValue("capOnUsers", Math.max(1, (userCapacity || 1) - 1));

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Company Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Company Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("companyName", { required: "Company name is required" })}
            placeholder="Company Name"
            className="h-10"
          />
          {errors.companyName && (
            <span className="text-xs text-red-500">{errors.companyName.message}</span>
          )}
        </div>

        {/* Admin Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Admin Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("adminName", { required: "Admin name is required" })}
            placeholder="Admin Name"
            className="h-10"
          />
          {errors.adminName && (
            <span className="text-xs text-red-500">{errors.adminName.message}</span>
          )}
        </div>

        {/* Admin Email */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId", {
              required: "Admin email is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Invalid email format",
              },
            })}
            placeholder="Admin Email"
            className="h-10"
          />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* Subscription Type - Dropdown */}
        <div className="flex w-full flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Subscription Type <span className="ml-1 text-red-500">*</span>
          </label>

          <Select
            onValueChange={(value) =>
              setValue("subscriptionType", value as CreateTenantPayload["subscriptionType"], {
                shouldValidate: true,
              })
            }
            defaultValue={watch("subscriptionType")}
          >
            <SelectTrigger className="h-10 w-full">
              <SelectValue placeholder="Select Subscription" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Default</SelectItem>
            </SelectContent>
          </Select>

          {errors.subscriptionType && (
            <span className="text-xs text-red-500">{errors.subscriptionType.message}</span>
          )}
        </div>

        {/* Contact Number */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Contact No <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("contactNo", {
              required: "Contact number is required",
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Contact number must be 10 digits",
              },
            })}
            placeholder="Contact Number"
            className="h-10"
          />
          {errors.contactNo && (
            <span className="text-xs text-red-500">{errors.contactNo.message}</span>
          )}
        </div>

        {/* Address */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Address <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("address", { required: "Address is required" })}
            placeholder="Address"
            className="h-10"
          />
          {errors.address && <span className="text-xs text-red-500">{errors.address.message}</span>}
        </div>

        {/* User Capacity with + / - */}
        <div className="flex flex-col space-y-1">
          <div className="flex items-center justify-between">
            <label
              htmlFor="capOnUsers"
              className="flex items-center text-sm font-medium text-gray-700"
            >
              User Cap
            </label>
            <div className="flex items-center gap-2">
              <Button type="button" variant="outline" onClick={decrement}>
                -
              </Button>
              <Input
                id="capOnUsers"
                type="text"
                {...register("capOnUsers", {
                  required: "User capacity is required",
                  min: { value: 1, message: "User capacity must be at least 1" },
                })}
                value={userCapacity}
                readOnly
                className="w-15 text-center"
              />
              <Button type="button" variant="outline" onClick={increment}>
                +
              </Button>
            </div>
          </div>
          {errors.capOnUsers && (
            <span className="text-xs text-red-500">{errors.capOnUsers.message}</span>
          )}
        </div>
      </div>

      <Button type="submit" className="w-full px-8 md:w-auto">
        {isPending ? "Registering..." : "Register Tenant"}
      </Button>
    </form>
  );
};

export default AddTenantForm;
