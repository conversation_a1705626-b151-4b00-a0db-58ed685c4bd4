import { z } from "zod";

export const addTenantValidation = z.object({
    companyName: z
        .string()
        .min(1, "Company name is required")
        .max(100, "Company name must be at most 100 characters"),
    adminName: z
        .string()
        .min(1, "Admin name is required")
        .max(100, "Admin name must be at most 100 characters"),
    emailId: z
        .string()
        .min(1, "Admin email is required")
        .email("Invalid email format"),
    subscriptionType: z.enum(["default"], {
        errorMap: () => ({ message: "Subscription type is required" }),
    }),
    contactNo: z
        .string()
        .regex(/^[0-9]{10}$/, "Contact number must be 10 digits"),
    address: z
        .string()
        .min(1, "Address is required")
        .max(200, "Address must be at most 200 characters"),
    capOnUsers: z
        .number()
        .min(1, "User capacity must be at least 1"),
});

export type CreateTenantPayload = z.infer<typeof addTenantValidation>;
