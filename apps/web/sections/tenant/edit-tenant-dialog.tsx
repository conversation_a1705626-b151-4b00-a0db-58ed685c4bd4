"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@repo/ui/components/dialog";
import EditTenantForm from "./edit-tenant-form";

export type Tenant = {
  id: string;
  tenantId: string;
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
  createdAt: string;
  updatedAt: string;
  first_time_user: boolean;
  users: Array<{
    id: string;
    firstName: string;
    lastName: string;
    displayName: string;
    emailId: string;
    role: string;
    isActive: boolean;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: any;
    first_time_user: boolean;
  }>;
};

interface EditTenantDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTenant: Tenant | null;
}

const EditTenantDialog: React.FC<EditTenantDialogProps> = ({
  open,
  onO<PERSON><PERSON>hang<PERSON>,
  selected<PERSON><PERSON><PERSON>,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="text-center">Edit Tenant</DialogTitle>
        </DialogHeader>
        {selectedTenant ? (
          <EditTenantForm selectedTenant={selectedTenant} onClose={() => onOpenChange(false)} />
        ) : (
          <p className="text-muted-foreground">No tenant selected</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditTenantDialog;
