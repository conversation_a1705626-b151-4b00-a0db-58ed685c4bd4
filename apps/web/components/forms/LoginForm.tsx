"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { useRouter, useSearchParams } from "next/navigation";
import logo from "@/public/LOGOPMS.png";
import logoCadetlabs from "@/public/logo.png";
import Image from "next/image";

export interface LoginFormProps {}

const schema = z.object({
  email: z.string().email("Enter a valid email"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormValues = z.infer<typeof schema>;

const LoginForm = React.forwardRef<HTMLDivElement, LoginFormProps>(({}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = React.useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectToPath = searchParams.get("redirectToPath") || "/dashboard";

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: LoginFormValues) => {
    setError(null);

    try {
      const response = await EmailPassword.signIn({
        formFields: [
          { id: "email", value: data.email },
          { id: "password", value: data.password },
        ],
      });

      switch (response.status) {
        case "FIELD_ERROR": {
          const fieldError = response?.formFields[0];
          if (fieldError) {
            setFormError(fieldError.id as keyof LoginFormValues, {
              type: "server",
              message: fieldError.error,
            });
          }
          break;
        }

        case "WRONG_CREDENTIALS_ERROR": {
          setError("Invalid email or password.");
          break;
        }

        case "OK": {
          setIsRedirecting(true);
          router.push(redirectToPath);
          break;
        }

        default: {
          setError("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    }
  };

  const handleForgotPassword = () => {
    router.push("/forgot-password");
  };

  // Combined loading state for better UX
  const isLoading = isSubmitting || isRedirecting;

  return (
    <div ref={ref} className="w-full max-w-lg">
      <div className="mb-1 flex flex-col items-end">
        <Image
          src={logoCadetlabs}
          alt="Cadet Labs"
          width={59}
          height={53}
          className="mr-2 object-cover"
        />
      </div>
      <Card className="w-full border-0 shadow-none">
        <CardHeader className="text-left">
          <div className="mb-1 flex flex-col items-start">
            <Image
              src={logo}
              alt="Cadet Labs"
              width={68}
              height={77}
              className="mr-2 object-cover"
            />
          </div>
          <div>
            <h1 className="text-primary text-2xl leading-snug">Log In</h1>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Email */}

            <Input
              id="email"
              type="email"
              placeholder="Email Address"
              disabled={isLoading}
              icon={<Icons.mail className="h-4 w-4" />}
              iconPosition="left"
              {...register("email")}
              className={errors.email && "border-red-500"}
            />
            {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}

            {/* Password */}
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                disabled={isLoading}
                icon={<Icons.lock className="h-4 w-4" />}
                iconPosition="left"
                {...register("password")}
                className={errors.password && "border-red-500"}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 hover:text-gray-600"
                disabled={isLoading}
              >
                {showPassword ? (
                  <Icons.eyeOff className="h-4 w-4" />
                ) : (
                  <Icons.eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}

            {/* Error message */}
            {error && <p className="text-sm text-red-500">{error}</p>}

            <div className="mt-2 text-right">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="cursor-pointer text-xs text-gray-500 underline hover:text-gray-700"
                disabled={isLoading}
              >
                Forgot Password?
              </button>
            </div>

            <div>
              <Button type="submit" className="w-full rounded-lg" disabled={isLoading}>
                {isRedirecting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Redirecting...
                  </>
                ) : isSubmitting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Logging in...
                  </>
                ) : (
                  <>
                    Login
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
});

LoginForm.displayName = "LoginForm";
export { LoginForm };
