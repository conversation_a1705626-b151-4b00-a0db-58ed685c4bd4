"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { useRouter, useSearchParams } from "next/navigation";
import logo from "@/public/logo.png";
import Image from "next/image";

export interface NewPasswordFormProps {}

const schema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string().min(6, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type NewPasswordFormValues = z.infer<typeof schema>;

const NewPasswordForm = React.forwardRef<HTMLDivElement, NewPasswordFormProps>(({}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
  } = useForm<NewPasswordFormValues>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: NewPasswordFormValues) => {
    setError(null);
    setSuccess(null);

    if (!token) {
      setError("Invalid reset token. Please request a new password reset link.");
      return;
    }

    try {
      const response = await EmailPassword.submitNewPassword({
        formFields: [{ id: "password", value: data.password }],
      });

      switch (response.status) {
        case "FIELD_ERROR": {
          const fieldError = response?.formFields[0];
          if (fieldError) {
            setFormError(fieldError.id as keyof NewPasswordFormValues, {
              type: "server",
              message: fieldError.error,
            });
          }
          break;
        }

        case "RESET_PASSWORD_INVALID_TOKEN_ERROR": {
          setError("This password reset link has expired. Please request a new one.");
          break;
        }

        case "OK": {
          setSuccess("Password updated successfully! Redirecting to login...");
          setTimeout(() => {
            router.push("/login");
          }, 2000);
          break;
        }

        default: {
          setError("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    }
  };

  // If no token, redirect to forgot password
  React.useEffect(() => {
    if (!token) {
      router.push("/forgot-password");
    }
  }, [token, router]);

  if (!token) {
    return null; // Will redirect
  }

  return (
    <div ref={ref} className="w-full max-w-lg">
      <Card className="w-full border-0 shadow-none">
        <CardHeader className="text-left">
          <div className="mb-4 flex flex-col items-start">
            <Image src={logo} alt="Cadet Labs" width={40} height={40} className="mr-2" />
          </div>
          <div>
            <h1 className="text-primary mb-0 text-3xl leading-snug font-bold">PMS Asset Builder</h1>
            <h1 className="text-primary text-2xl leading-snug">Set New Password</h1>
            <p className="mt-2 text-xs text-gray-500">Please enter your new password below</p>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Password */}
            <div className="space-y-1">
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="New Password"
                  disabled={isSubmitting}
                  icon={<Icons.lock className="h-4 w-4" />}
                  iconPosition="left"
                  {...register("password")}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute top-1/2 right-3 -translate-y-1/2 hover:text-gray-600"
                  disabled={isSubmitting}
                >
                  {showPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
            </div>

            {/* Confirm Password */}
            <div className="space-y-1">
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm New Password"
                  disabled={isSubmitting}
                  icon={<Icons.lock className="h-4 w-4" />}
                  iconPosition="left"
                  {...register("confirmPassword")}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute top-1/2 right-3 -translate-y-1/2 hover:text-gray-600"
                  disabled={isSubmitting}
                >
                  {showConfirmPassword ? (
                    <Icons.eyeOff className="h-4 w-4" />
                  ) : (
                    <Icons.eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
              )}
            </div>

            {/* Success message */}
            {success && <p className="text-sm text-green-600">{success}</p>}

            {/* Error message */}
            {error && <p className="text-sm text-red-500">{error}</p>}

            <div>
              <Button
                type="submit"
                className="w-full rounded-lg"
                disabled={isSubmitting || !!success}
              >
                {isSubmitting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Setting Password...
                  </>
                ) : (
                  <>
                    Set New Password
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
});

NewPasswordForm.displayName = "NewPasswordForm";
export { NewPasswordForm };
