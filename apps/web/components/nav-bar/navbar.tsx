"use client";

import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";

import logo from "../../public/logo.png";
import personPic from "../../public/person.png";

import { Icons } from "@repo/ui/components/icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

import { signOut } from "supertokens-auth-react/recipe/emailpassword";
import { useMetaData } from "../meta-data-provider";
import TenantSelector from "./tenant-selector";
import { CanAccess } from "@/access-control";
import { useLogout } from "@/api-slice/common";
import { Button } from "@repo/ui/components/button";

dayjs.extend(updateLocale);
dayjs.locale("en");

// Types for menu items
type MenuItem = {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  access?: {
    resource: string;
    privilege: "create" | "update" | "delete" | "read";
  };
};

// ✅ Menu configuration outside component (no re-creation on every render)
const MENU_ITEMS: MenuItem[] = [
  {
    title: "User Management",
    description: "Manage users, roles, and permissions.",
    icon: Icons.users,
    path: "/users",
    access: {
      resource: "user",
      privilege: "read" as const,
    },
  },
  {
    title: "Tenant Management",
    description: "Manage tenants, roles, and permissions.",
    icon: Icons.boxes,
    path: "/tenant",
    access: {
      resource: "tenant",
      privilege: "read" as const,
    },
  },
  {
    title: "Help & Support",
    description: "Get help, view documentation, or contact support.",
    icon: Icons.headset,
    path: "/help",
    // No access control - always visible
  },
];

const Navbar: React.FC = () => {
  const router = useRouter();
  const logout = useLogout();

  const { metaData } = useMetaData();

  console.log("metadata", metaData);

  const { user } = metaData;

  const handleRoute = async (path: string) => {
    router.push(path);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error("Logout failed:", err);
    }
  };

  return (
    <nav className="flex h-[80px] shrink-0 border bg-white px-4">
      <div className="flex w-full items-center justify-between px-4">
        {/* Left: Logo */}
        <Image src={logo} alt="Cadetlabs" className="h-[49px] w-[56px] object-contain" priority />

        {/* Right: Actions */}

        <div className="flex items-center space-x-6">
          <CanAccess resource="tenant" privilege="read">
            <TenantSelector />
          </CanAccess>
          {/* Divider */}
          <span className="h-6 w-px bg-gray-300" />

          {/* User Profile */}
          <div className="flex items-center space-x-3">
            {/* User Info + Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  className="flex items-center space-x-2 rounded-md px-2 py-1 transition hover:bg-gray-50 focus:outline-none"
                  onClick={() => console.log("Profile clicked")} // or handleRoute('/profile')
                >
                  {/* Profile Image */}
                  <div className="bg-primary flex h-[43px] w-[44px] items-center justify-center overflow-hidden rounded-md">
                    <span className="text-lg font-semibold text-white">
                      {user?.displayName?.charAt(0).toUpperCase() || "A"}
                    </span>
                  </div>

                  {/* User Info */}
                  <div className="text-left leading-tight">
                    <h1 className="text-sm font-medium text-gray-800 capitalize">
                      {user?.displayName || "NA"}
                    </h1>
                    <p className="text-[11px] text-gray-500">{user?.role || "NA"}</p>
                  </div>

                  <Icons.chevronDown className="size-4 text-gray-500" />
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent className="w-[300px]" side="bottom" align="end" sideOffset={4}>
                <DropdownMenuItem>
                  <div className="flex items-start gap-3 p-2">
                    <div className="flex h-[43px] w-[44px] items-center justify-center overflow-hidden rounded-md border-black bg-white">
                      <span className="text-lg font-semibold text-orange-300">
                        {user?.displayName?.charAt(0).toUpperCase() || "A"}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <div className="leading-tight">
                        <h1 className="text-sm font-medium text-gray-800 capitalize">
                          {user?.displayName || "NA"}
                        </h1>
                        <p className="text-[11px] text-gray-500">{user?.role || "NA"}</p>
                        <p className="text-[11px] text-gray-500">{user?.emailId || "NA"}</p>
                      </div>
                      <Button className="mt-3 cursor-pointer" size="sm" onClick={handleLogout}>
                        Log Out
                      </Button>
                    </div>
                  </div>
                </DropdownMenuItem>

                {MENU_ITEMS.map(({ title, description, icon: Icon, path, access }) => {
                  const menuItem = (
                    <DropdownMenuItem
                      key={title}
                      className="cursor-pointer"
                      onClick={() => handleRoute(path)}
                    >
                      <div className="flex items-start gap-3 p-2">
                        <Icon className="size-7 text-teal-500" />
                        <div className="flex flex-col">
                          <h1 className="text-sm font-medium text-gray-900">{title}</h1>
                          <p className="text-xs text-gray-500">{description}</p>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  );

                  return access ? (
                    <CanAccess key={title} resource={access.resource} privilege={access.privilege}>
                      {menuItem}
                    </CanAccess>
                  ) : (
                    menuItem
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Notification + Settings */}
            <div className="flex items-center space-x-3">
              {[Icons.bell].map((Icon, i) => (
                <div
                  key={i}
                  className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full border border-gray-200 transition hover:bg-gray-100"
                >
                  <Icon className="size-4 text-gray-600" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
