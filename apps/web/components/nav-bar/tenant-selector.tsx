"use client";

import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@repo/ui/components/dropdown-menu";
import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";
import { Button } from "@repo/ui/components/button";
import { ChevronDown, Loader2 } from "lucide-react";
import { Spinner } from "@repo/ui/components/spinner";
import { useGetTenantList } from "@/api-slice/tenant";
import { useTenant } from "@/lib/tenant-context";

const TenantSelector: React.FC<{ className?: string }> = ({ className }) => {
  const { currentTenantId, setCurrentTenantId } = useTenant();
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error } =
    useGetTenantList(5);

  const tenants = data?.pages?.flatMap((p: any) => p.tenants || []) || [];
  const currentTenant = tenants.find((t: any) => t.tenantId === currentTenantId);

  const handleTenantChange = (tenant: any) => {
    setCurrentTenantId(tenant.tenantId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <Spinner />
        <span>Loading Tenants...</span>
      </div>
    );
  }

  if (error) {
    return <div className="text-sm text-red-500">Error loading tenants</div>;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`hover:bg-accent/10 flex items-center gap-2 rounded-md px-2 py-1.5 ${className}`}
        >
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-primary text-sm font-semibold text-white">
              {currentTenant?.companyName?.[0]?.toUpperCase() || "?"}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col text-left">
            <span className="text-sm leading-none font-medium">
              {currentTenant?.companyName || "Select tenant"}
            </span>
            <span className="text-muted-foreground text-xs capitalize">
              {currentTenant?.type || ""}
            </span>
          </div>
          <ChevronDown className="ml-1 h-4 w-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="border-border bg-background w-64 border p-2 shadow-md"
        align="start"
        sideOffset={6}
      >
        {tenants.map((tenant: any) => (
          <DropdownMenuItem
            key={tenant.id}
            onClick={() => handleTenantChange(tenant)}
            className={`flex items-center gap-2 rounded-md px-2 py-2 transition-all ${
              tenant.tenantId === currentTenantId
                ? "bg-accent/10 font-semibold"
                : "hover:bg-accent/10"
            }`}
          >
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-teal-500 font-semibold text-white">
                {tenant.companyName?.[0]?.toUpperCase() || "?"}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm leading-none">{tenant.companyName}</span>
              <span className="text-muted-foreground text-xs capitalize">{tenant.type}</span>
            </div>
          </DropdownMenuItem>
        ))}

        {hasNextPage && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              disabled={isFetchingNextPage}
              onClick={(e) => {
                e.stopPropagation();
                fetchNextPage();
              }}
              className="hover:bg-accent/10 flex justify-center text-sm"
            >
              {isFetchingNextPage ? <Loader2 className="h-4 w-4 animate-spin" /> : "Load more"}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default TenantSelector;
