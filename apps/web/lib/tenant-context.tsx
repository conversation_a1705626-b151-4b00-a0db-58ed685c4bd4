"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useMetaData } from "@/components/meta-data-provider";

interface TenantContextType {
  currentTenantId: string | null;
  setCurrentTenantId: (tenantId: string | null) => void;
  clearTenantData: () => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [currentTenantId, setCurrentTenantIdState] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { metaData } = useMetaData();

  // Initialize tenant on mount: localStorage first, then metadata
  useEffect(() => {
    const savedTenantId = localStorage.getItem("currentTenantId");
    if (savedTenantId) {
      setCurrentTenantIdState(savedTenantId);
      console.log("Initializing tenant from localStorage:", savedTenantId);
      return;
    }

    const metaTenantId = metaData?.user?.tenantId;
    if (metaTenantId) {
      setCurrentTenantIdState(metaTenantId);
      console.log("Initializing tenant from metaData:", metaTenantId);
    }
  }, []);

  const setCurrentTenantId = (tenantId: string | null) => {
    if (tenantId === currentTenantId) return;

    setCurrentTenantIdState(tenantId);
    queryClient.clear();

    if (tenantId) {
      localStorage.setItem("currentTenantId", tenantId);
    } else {
      localStorage.removeItem("currentTenantId");
    }

    // Optional: invalidate queries after short delay
    setTimeout(() => queryClient.invalidateQueries(), 100);
  };

  const clearTenantData = () => {
    setCurrentTenantIdState(null);
    queryClient.clear();
    localStorage.removeItem("currentTenantId");
  };

  return (
    <TenantContext.Provider
      value={{
        currentTenantId,
        setCurrentTenantId,
        clearTenantData,
      }}
    >
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (!context) throw new Error("useTenant must be used within a TenantProvider");
  return context;
}
