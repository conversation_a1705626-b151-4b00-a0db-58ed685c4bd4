import { StaffUserService } from "../services/StaffUserService";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { getTenantIdFromRequest } from "../middleware/tenantHeader";

export class StaffUserController {
  // Get all users
  static async getAllUsers(req: any, res: any) {
    try {
      // Get tenant ID from header (set by frontend tenant selector)
      const headerTenantId = getTenantIdFromRequest(req);

      // Get user metadata for role-based access control
      const metadata = req.metadata || {};
      const userRole = metadata.role;
      const userTenantId = headerTenantId || metadata.tenantId;

      const users = await StaffUserService.getAllUsers(userTenantId);
      res.json(users);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  }

  // Get user by ID
  static async getUserById(req: any, res: any) {
    try {
      const headerTenantId = getTenantIdFromRequest(req);
      // Get tenant context from metadata
      const metadata = req.metadata || {};
      const userRole = metadata.role;

      const userTenantId = headerTenantId || metadata.tenantId;

      // Platform admins (superadmin) can see any user, others see only their tenant users
      const tenantId = userTenantId;

      const user = await StaffUserService.getUserById(req.params.id, tenantId);
      res.json(user);
    } catch (error: any) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Create new user
  static async createUser(req, res) {
    try {
      const userId = req.session!.getUserId();
      const { metadata } = await UserMetadata.getUserMetadata(userId);
      const headerTenantId = getTenantIdFromRequest(req);
      const userTenantId = headerTenantId || metadata.tenantId;

      console.log("metadata", metadata);
      const user = await StaffUserService.createUser(req.body, {
        ...metadata,
        tenantId: userTenantId,
      });
      res.status(201).json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Update user
  static async updateUser(req, res) {
    try {
      const headerTenantId = getTenantIdFromRequest(req);
      // Get tenant context from metadata
      const metadata = req.metadata || {};
      const userRole = metadata.role;

      const userTenantId = headerTenantId || metadata.tenantId;

      const user = await StaffUserService.updateUser(req.params.id, req.body, {
        ...metadata,
        tenantId: userTenantId,
      });
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Delete user (if needed)
  static async deleteUser(req, res) {
    try {
      const user = await StaffUserService.deleteUser(req.params.id);
      res.json({ message: "User deleted successfully", user });
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async listUsers(req: any, res: any) {
    try {
      const { limit, cursor } = req.body;

      // Get tenant ID from header (set by frontend tenant selector)
      const headerTenantId = getTenantIdFromRequest(req);

      // Get user metadata for role-based access control
      const metadata = req.metadata || {};
      const userRole = metadata.role;
      const userTenantId = headerTenantId || metadata.tenantId;

      console.log("metadata full", metadata);

      const result = await StaffUserService.listUsers({ limit, cursor, tenantId: userTenantId });
      res.json(result);
    } catch (error: any) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  static async activateUser(req, res) {
    try {
      // Get tenant ID from header (set by frontend tenant selector)
      const headerTenantId = getTenantIdFromRequest(req);

      // Get user metadata for role-based access control
      const metadata = req.metadata || {};
      const userRole = metadata.role;
      const userTenantId = headerTenantId || metadata.tenantId;

      const user = await StaffUserService.activateUser(req.params.id, {
        ...metadata,
        tenantId: userTenantId,
      });
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async deactivateUser(req, res) {
    try {
      // Get tenant ID from header (set by frontend tenant selector)
      const headerTenantId = getTenantIdFromRequest(req);

      // Get user metadata for role-based access control
      const metadata = req.metadata || {};
      const userRole = metadata.role;
      const userTenantId = headerTenantId || metadata.tenantId;

      console.log("deactivate", req.params.id, metadata);

      const user = await StaffUserService.deactivateUser(req.params.id, {
        ...metadata,
        tenantId: userTenantId,
      });
      console.log("Deactivated User", user);
      res.json(user);
    } catch (error) {
      console.log(error);
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
  static async resendEmailVerification(req, res) {
    try {
      const headerTenantId = getTenantIdFromRequest(req);
      // Get tenant context from metadata
      const metadata = req.metadata || {};
      const userRole = metadata.role;

      const userTenantId = headerTenantId || metadata.tenantId;

      const user = await StaffUserService.resendEmailVerification(req.params.id, {
        ...metadata,
        tenantId: userTenantId,
      });
      res.json(user);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
}
