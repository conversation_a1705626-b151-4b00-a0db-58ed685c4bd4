import { Request, Response, NextFunction } from "express";

/**
 * Middleware to extract tenant ID from request headers and attach to request object
 */
export const tenantHeaderMiddleware = (req: any, res: Response, next: NextFunction) => {
  try {
    // If not superadmin, skip tenant header logic
    if (req.metadata?.role !== "superadmin") {
      req.tenantId = req.metadata?.tenantId;
      console.log("Not Superadmin, using metadata");
      return next(); // ✅ return to prevent double next()
    }

    console.log("Superadmin, using x-tenant-id");
    const tenantId = req.headers["x-tenant-id"] as string;

    if (tenantId) {
      req.tenantId = tenantId;
      console.log(`Request with tenant ID: ${tenantId}`);
    } else {
      req.tenantId = null;
      console.log("Request without tenant ID (platform admin or no tenant selected)");
    }

    next();
  } catch (error) {
    console.error("Error in tenant header middleware:", error);
    next(error); // ✅ Pass error to Express error handler
  }
};

/**
 * Helper function to get tenant ID from request
 */
export const getTenantIdFromRequest = (req: any): string | null => {
  return req.tenantId || null;
};
