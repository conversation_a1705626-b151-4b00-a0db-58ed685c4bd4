import { initializeDatabase, type DatabaseConfig } from "@repo/database";

export const initializeDatabaseConnection = async () => {
  const dbConfig: DatabaseConfig = {
    user: process.env.APP_POSTGRESQL_USER,
    password: process.env.APP_POSTGRESQL_PASSWORD,
    host: process.env.POSTGRESQL_HOST,
    port: parseInt(process.env.POSTGRESQL_PORT),
    database: process.env.POSTGRESQL_DATABASE_NAME,
    schema: process.env.APP_POSTGRESQL_TABLE_SCHEMA,
  };

  await initializeDatabase(dbConfig);
};
