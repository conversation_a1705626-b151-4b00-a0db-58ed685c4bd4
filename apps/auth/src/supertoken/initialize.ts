import supertokens from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { middleware, errorHandler } from "supertokens-node/framework/express";
import { SMTPService } from "supertokens-node/recipe/emailpassword/emaildelivery";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { StaffUserService } from "../services/StaffUserService";

export interface SuperTokensConfig {
  connectionURI: string;
  appInfo: {
    appName: string;
    apiDomain: string;
    websiteDomain: string;
    apiBasePath?: string;
    websiteBasePath?: string;
  };
}
export class SuperTokensService {
  private config: SuperTokensConfig;
  private initialized = false;

  constructor(config: SuperTokensConfig) {
    this.config = config;
  }

  /**
   * Initialize SuperTokens with the provided configuration
   */
  init(): void {
    supertokens.init({
      framework: "express",
      supertokens: {
        connectionURI: "http://localhost:3567",
      },
      appInfo: {
        appName: "CadetLabs",
        apiDomain: process.env.SERVER_DOMAIN || "http://localhost:3005", // Auth app domain
        websiteDomain: process.env.WEBSITE_DOMAIN || "http://localhost:3000", // Frontend domain
        apiBasePath: "/auth",
        websiteBasePath: "/auth",
      },
      recipeList: [
        EmailVerification.init({
          mode: "REQUIRED", // or "OPTIONAL"
          override: {
            apis: (originalImplementation) => {
              return {
                ...originalImplementation,
                verifyEmailPOST: async (input) => {
                  console.log("Verify Email Post triggered");
                  if (originalImplementation.verifyEmailPOST === undefined) {
                    throw new Error("verifyEmailPOST not implemented in originalImplementation");
                  }

                  // Call the original implementation first
                  const response = await originalImplementation.verifyEmailPOST(input);

                  // If email verification was successful, activate the user
                  if (response.status === "OK") {
                    try {
                      const email = response.user.email;
                      console.log(`Email verified successfully for: ${email}`);

                      // Activate user in our database
                      await StaffUserService.activateUserAfterEmailVerification(email);
                      console.log(`User ${email} activated after email verification`);
                    } catch (error) {
                      console.error("Error activating user after email verification:", error);
                      // Don't fail the email verification if user activation fails
                      // The user can still be manually activated later
                    }
                  }

                  return response;
                },
              };
            },
          },
        }),
        EmailPassword.init({
          override: {
            apis: (originalImplementation) => {
              return {
                ...originalImplementation,
                signInPOST: async (input) => {
                  if (originalImplementation.signInPOST === undefined) {
                    throw new Error("signInPOST not implemented in originalImplementation");
                  }

                  console.log("Signin input:", input);
                  const email = input.formFields.find((f) => f.id === "email")?.value;
                  console.log("Signin input email:", email);

                  const isActive = await StaffUserService.checkEmailIsActive(email as string);
                  console.log("Signin input email is active:", isActive);

                  if (!isActive) {
                    return {
                      status: "CUSTOM_ERROR",
                      message: "User is deactivated. Please contact support.",
                    };
                  }

                  // Call original implementation
                  return await originalImplementation.signInPOST(input);
                },
                signUpPOST: undefined,
              };
            },
          },
        }),

        UserMetadata.init(),
        Session.init({
          cookieSecure: process.env.NODE_ENV === "production",
          cookieSameSite: "lax",
          exposeAccessTokenToFrontendInCookieBasedAuth: true,
        }),
      ],
    });

    this.initialized = true;
  }
  getMiddleware() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return middleware();
  }
}
export function createDefaultSuperTokensService(): SuperTokensService {
  return new SuperTokensService({
    connectionURI: "http://localhost:3567",
    appInfo: {
      appName: "CadetLabs",
      apiDomain: "http://localhost:3005", // Auth app domain
      websiteDomain: "http://localhost:3000", // Frontend domain
      apiBasePath: "/auth",
      websiteBasePath: "/auth",
    },
  });
}
