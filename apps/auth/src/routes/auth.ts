import { Router } from "express";
import { verifySession } from "supertokens-node/recipe/session/framework/express";

// Import all controllers
import { AuthController } from "../controllers/authController";
import { UserController } from "../controllers/userController";
import { EmailController } from "../controllers/emailController";
import { PasswordController } from "../controllers/passwordController";
import { SessionController } from "../controllers/sessionContoller";
import { HealthController } from "../controllers/healthController";
import { MetadataController } from "../controllers/metadataController";

export const createAuthRouter = () => {
  const router = Router();

  // Health check routes
  router.get("/health", HealthController.healthCheck);

  // Authentication routes
  // router.post("/signin", AuthController.signIn);
  router.post("/signup", AuthController.signUp);
  router.post("/signout", verifySession(), AuthController.signOut);
  router.post("/get-metadata-by-email", MetadataController.getMetadataByEmail);
  router.post("/set-metadata-by-email", MetadataController.setMetadataByEmail);
  // User management routes
  router.get("/me", verifySession(), UserController.getCurrentUser);
  router.post("/create-user", UserController.createUser);
  router.get("/signup/email/exists", UserController.checkEmailExists);
  router.put("/user/metadata", verifySession(), UserController.updateUserMetadata);
  router.delete("/user/metadata", verifySession(), UserController.clearUserMetadata);
  router.post("/user/email/verify/send", EmailController.sendEmailVerification);
  router.post("/user/email/verify", EmailController.verifyEmailToken);
  router.post("/user/password/reset/token", PasswordController.sendPasswordResetEmail);

  return router;
};
