import { <PERSON>ail<PERSON>lient } from "@azure/communication-email";
import * as fs from "fs/promises";
import * as path from "path";

export class EmailService {
  private static emailClient: EmailClient;

  // Initialize the email client (call this once in your application startup)
  static initialize(connectionString: string) {
    this.emailClient = new EmailClient(connectionString);
  }

  static async sendEmailWithTemplate(
    content: { to: string; subject: string; data: object },
    template: string
  ) {
    try {
      // Ensure email client is initialized
      if (!this.emailClient) {
        throw new Error("EmailClient not initialized. Call EmailService.initialize() first.");
      }

      // Default template path
      const templateFile = template;

      // Read the HTML template
      const htmlTemplate = await this.readTemplate(templateFile);

      // Replace placeholders in the template with actual data
      const htmlContent = this.replacePlaceholders(htmlTemplate, content.data);

      // Prepare the email message
      const emailMessage = {
        senderAddress: "<EMAIL>",
        content: {
          subject: content.subject,
          html: htmlContent,
        },
        recipients: {
          to: [
            {
              address: content.to,
            },
          ],
        },
      };

      // Send the email
      const poller = await this.emailClient.beginSend(emailMessage);
      const result = await poller.pollUntilDone();

      console.log(`Email sent successfully. Message ID: ${result.id}`);
      return result;
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  }

  // Helper method to read template file
  private static async readTemplate(templateFile: string): Promise<string> {
    try {
      const absolutePath = path.resolve(__dirname, `../templates/${templateFile}.html`);
      const template = await fs.readFile(absolutePath, "utf-8");
      return template;
    } catch (error) {
      console.error(`Error reading template file: ${templateFile}`, error);
      throw new Error(`Failed to read email template: ${templateFile}`);
    }
  }

  // Helper method to replace placeholders in template
  private static replacePlaceholders(template: string, data: object): string {
    let result = template;

    for (const [key, value] of Object.entries(data)) {
      // Replace placeholders like {{key}} or {key}
      const placeholder1 = new RegExp(`{{${key}}}`, "g");
      const placeholder2 = new RegExp(`{${key}}`, "g");

      result = result.replace(placeholder1, String(value));
      result = result.replace(placeholder2, String(value));
    }

    return result;
  }

  // Optional: Method to send plain text email without template
  static async sendPlainEmail(
    to: string,
    subject: string,
    content: string,
    isHtml: boolean = false
  ) {
    try {
      // Create email client using environment variable
      const connectionString = process.env.AZURE_COMMUNICATION_CONNECTION_STRING;
      if (!connectionString) {
        throw new Error("AZURE_COMMUNICATION_CONNECTION_STRING environment variable is required.");
      }

      const emailClient = new EmailClient(connectionString);

      const emailMessage = {
        senderAddress: "<EMAIL>",
        content: {
          subject: subject,
          ...(isHtml ? { html: content } : { plainText: content }),
        },
        recipients: {
          to: [{ address: to }],
        },
      };

      const poller = await emailClient.beginSend(emailMessage);
      const result = await poller.pollUntilDone();

      console.log(`Plain email sent successfully. Message ID: ${result.id}`);
      return result;
    } catch (error) {
      console.error("Error sending plain email:", error);
      throw error;
    }
  }
}
