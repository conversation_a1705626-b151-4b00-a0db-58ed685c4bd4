import { getPrisma, getPrismaWithTenant, type PrismaWithTenant } from "@repo/database";
import { UserController } from "../controllers/userController";
import { rbacRules } from "../utils/rbac-rule";
import { EmailService } from "./emailService";
import { EmailController } from "../controllers/emailController";
import { StaffUserController } from "../controllers/staffUserController";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { listUsersByAccountInfo } from "supertokens-node";

export class StaffUserService {
  // Helper function to generate display name
  static generateDisplayName(firstName, lastName) {
    return `${firstName.trim()} ${lastName.trim()}`.trim();
  }

  // Get all users with optional tenant filtering
  static async getAllUsers(tenantId?: string) {
    let prisma;

    if (tenantId) {
      // Use tenant extension for automatic RLS filtering
      prisma = getPrismaWithTenant(tenantId);
    } else {
      // Use regular prisma (for platform admins)
      prisma = getPrisma();
    }

    const users = await prisma.$queryRaw`
      SELECT
    u.id,
    u.first_name AS "firstName",
    u.last_name AS "lastName",
    u.display_name AS "displayName",
    u.email_id AS "emailId",
    u.role AS "role",
    u.status AS "status",
    u.tenant_id AS "tenantId",
    CASE
      WHEN verified_email.email IS NOT NULL THEN true
      ELSE false
    END AS email_verified
  FROM app."user" AS u
  LEFT JOIN auth.emailverification_verified_emails AS verified_email
    ON verified_email.email = u.email_id
    `;
    return users;
  }

  // Get user by ID with optional tenant filtering
  static async getUserById(userId: string, tenantId?: string) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    let prisma;

    if (tenantId) {
      // Use tenant extension for automatic RLS filtering
      prisma = getPrismaWithTenant(tenantId);
    } else {
      // Use regular prisma (for platform admins)
      prisma = getPrisma();
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenant: true,
      },
    });

    if (!user) {
      const error = new Error("User not found") as any;
      error.statusCode = 404;
      throw error;
    }

    return user;
  }

  // Create new user
  static async createUser(userData: any, metadata: any) {
    const { firstName, lastName, displayName, emailId, role, type } = userData;
    const tenantId = metadata.tenantId;

    console.log("createUser", userData, metadata);

    // Basic validation
    if (!firstName || !lastName || !emailId || !role) {
      const error = new Error(
        "firstName, lastName, emailId, role, and tenantId are required"
      ) as any;
      error.statusCode = 400;
      throw error;
    }

    // Use tenant extension for RLS-aware operations
    const prisma = getPrismaWithTenant(tenantId);

    // Check if tenant exists
    const tenant = await prisma.tenant.findFirst({
      where: { tenantId: tenantId },
    });

    console.log("Tenant exist", tenant);

    if (!tenant) {
      const error = new Error("Invalid tenant ID") as any;
      error.statusCode = 400;
      throw error;
    }

    // Check user cap
    const currentUserCount = await prisma.user.count({
      where: { tenantId: tenantId },
    });

    console.log("currentUserCount", currentUserCount);

    if (currentUserCount >= tenant.capOnUsers) {
      const error = new Error(
        `Cannot create user. Tenant has reached maximum user limit of ${tenant.capOnUsers}`
      ) as any;
      error.statusCode = 400;
      throw error;
    }

    try {
      // First create user in SuperTokens (external service)
      // If this fails, nothing is written to DB
      const supertokenUserId = await UserController.createUserDirectly(emailId, "password123", {
        firstName,
        lastName,
        displayName: displayName || StaffUserService.generateDisplayName(firstName, lastName),
        emailId,
        tenantId,
        role: role,
        firstTimeUser: true,
        previlages: { resource: rbacRules[role]?.resource },
        type,
      });

      console.log("SuperToken user created:", supertokenUserId, tenantId);

      // Now create in database with transaction
      // If this fails, we should rollback SuperTokens user (handle in catch)
      const user = await prisma.$transaction(async (tx) => {
        // Create user
        const newUser = await tx.user.create({
          data: {
            firstName,
            lastName,
            displayName: displayName || StaffUserService.generateDisplayName(firstName, lastName),
            emailId,
            role,
            tenantId,
            status: "pending_verification", // Set to pending verification initially
          },
        });

        // Create user lookup entry
        await tx.userLookup.upsert({
          where: { emailId: emailId },
          update: { tenantId: tenantId },
          create: {
            emailId: emailId,
            tenantId: tenantId,
          },
        });

        return newUser;
      });

      console.log("User created in DB:", user.id);

      return user;
    } catch (error: any) {
      // If database operation failed after SuperTokens user was created,
      // we should delete the SuperTokens user to maintain consistency
      if (error.code === "P2002") {
        // Email already exists in DB
        // TODO: Delete SuperTokens user here if needed
        // await UserController.deleteUserDirectly(emailId);

        const customError = new Error("Email already exists") as any;
        customError.statusCode = 400;
        throw customError;
      }

      // For any other database errors, consider cleaning up SuperTokens user
      // TODO: Implement cleanup logic
      // await UserController.deleteUserDirectly(emailId);

      throw error;
    }
  }

  static async getOneUserByEmail(emailId: string) {
    try {
      const prisma = getPrisma();
      const user = await prisma.user.findFirst({
        where: { emailId: emailId },
      });
      return user;
    } catch (error) {}
  }
  // Update user
  static async updateUser(userId, updateData, metadata: any = {}) {
    if (!userId) {
      const error = new Error("User ID is required");
      error.statusCode = 400;
      throw error;
    }

    console.log("Updating user:", userId, updateData, metadata);

    // Use the current user's tenantId, not from updateData
    const prisma = getPrismaWithTenant(metadata.tenantId);

    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    // Check if role is being changed from admin
    if (updateData.role && updateData.role !== "admin" && currentUser?.role === "admin") {
      const adminCount = await prisma.user.count({
        where: {
          role: "admin",
        },
      });

      console.log("Admin count:", adminCount);

      if (adminCount <= 1) {
        console.log("Cannot remove admin role. At least one admin user must remain.");
        const error = new Error("Cannot remove admin role. At least one admin user must remain.");
        error.statusCode = 400;
        throw error;
      }
    }

    // Remove tenantId from updateData if present (should not be updatable)
    const { tenantId, ...safeUpdateData } = updateData;

    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data: safeUpdateData,
      });
      console.log("Update Metadata");
      const users = await listUsersByAccountInfo("public", { email: currentUser?.emailId });
      if (!users || users.length === 0) {
        new Error("User not found");
      }
      console.log("User found:", users[0]);

      const superTokenuserId = users[0]?.id;

      await UserMetadata.updateUserMetadata(superTokenuserId as string, {
        ...safeUpdateData,
        ...(safeUpdateData.role
          ? {
              previlages: {
                resource: rbacRules[safeUpdateData.role]?.resource,
              },
            }
          : {}),
      });

      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      } else if (error.code === "P2002") {
        const customError = new Error("Email already exists");
        customError.statusCode = 400;
        throw customError;
      }
      throw error;
    }
  }
  // Delete user (if needed)
  static async deleteUser(userId) {
    if (!userId) {
      const error = new Error("User ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    try {
      const user = await prisma.user.delete({
        where: { id: userId },
      });

      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }

  // This method seems to be used by other controllers, keeping it as is
  static async createUserDirectly(emailId, password, userData) {
    // Implementation for creating user directly
    // This method appears to be called by other parts of the application
    // You may need to implement this based on your existing UserController
    console.log("Creating user directly:", emailId, userData);
  }

  static async listUsers({
    limit = 10,
    cursor,
    tenantId,
  }: {
    limit?: number;
    cursor?: string;
    tenantId?: string;
  } = {}) {
    let prisma;

    if (tenantId) {
      // Use tenant extension for automatic RLS filtering
      prisma = getPrismaWithTenant(tenantId);
    } else {
      // Use regular prisma (for platform admins)
      prisma = getPrisma();
    }

    const take = Math.min(Math.max(1, Number(limit) || 10), 100);

    const query: any = {
      take: take + 1,
      orderBy: { createdAt: "desc" }, // better than UUID ordering
    };

    if (cursor) {
      query.cursor = { id: cursor }; // UUID works fine here
      query.skip = 1;
    }

    const results = await prisma.user.findMany(query);

    const hasMore = results.length > take;
    const users = results.slice(0, take);
    const nextCursor = hasMore ? users[users.length - 1].id : null;

    return {
      users,
      hasMore,
      nextCursor,
    };
  }

  static async activateUser(userId, metadata: any = {}) {
    if (!userId) {
      const error = new Error("User ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrismaWithTenant(metadata.tenantId as string);

    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data: {
          status: "active",
        },
      });

      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }
  static async deactivateUser(userId: string, metadata: any = {}) {
    if (!userId) {
      const error = new Error("User ID is required");
      // @ts-ignore
      error.statusCode = 400;
      throw error;
    }

    console.log("Deactivate Request", userId, metadata);

    const prisma = getPrismaWithTenant(metadata.tenantId as string);

    try {
      // 1️⃣ Fetch target user
      const user = await prisma.user.findUnique({ where: { id: userId } });
      if (!user) {
        const error = new Error("User not found");
        // @ts-ignore
        error.statusCode = 404;
        throw error;
      }

      // 2️⃣ Prevent self-deactivation
      if (user.emailId === metadata.emailId) {
        const error = new Error("You cannot deactivate your own account");
        // @ts-ignore
        error.statusCode = 403;
        throw error;
      }

      // 3️⃣ Ensure at least one admin remains active
      if (user.role === "admin") {
        const activeAdmins = await prisma.user.count({
          where: {
            role: "admin",
            status: "active",
          },
        });

        if (activeAdmins <= 1) {
          const error = new Error("At least one active admin must remain");
          // @ts-ignore
          error.statusCode = 400;
          throw error;
        }
      }

      // 4️⃣ Deactivate user
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { status: "inactive" },
      });

      console.log("Deactivated Request", updatedUser);

      // 5️⃣ Send notification email
      EmailService.sendEmailWithTemplate(
        {
          to: updatedUser.emailId,
          subject: "User Deactivated",
          data: { appName: "CadetLabs", ...updatedUser },
        },
        "user-deactivated"
      );

      return updatedUser;
    } catch (error: any) {
      console.log(error);
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        // @ts-ignore
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }

  // Method to activate user after email verification
  static async activateUserAfterEmailVerification(emailId: string, metadata: any = {}) {
    if (!emailId) {
      const error = new Error("Email ID is required") as any;
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    const userlookup = await prisma.userLookup.findFirst({
      where: { emailId: emailId },
    });
    const tenantId = userlookup?.tenantId;

    console.log("tenantId", tenantId);

    const prismaWithTenant = getPrismaWithTenant(tenantId as string);

    try {
      const user = await prismaWithTenant.user.update({
        where: { emailId: emailId },
        data: {
          status: "active",
        },
      });

      console.log(`User ${emailId} activated after email verification`);
      return user;
    } catch (error: any) {
      console.log(error);
      if (error.code === "P2025") {
        const customError = new Error("User not found") as any;
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }

  static async resendEmailVerification(userId: string, metadata: any = {}) {
    if (!userId) {
      const error = new Error("User ID is required") as any;
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrismaWithTenant(metadata.tenantId as string);

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });
      if (!user) {
        const error = new Error("User not found") as any;
        error.statusCode = 404;
        throw error;
      }
      await EmailController.resendEmailVerificationToUserByEmail(user.emailId, user);
      return user;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("User not found");
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }
  static async checkEmailIsActive(emailId: string) {
    const prisma = getPrisma();
    const userlookup = await prisma.userLookup.findFirst({
      where: { emailId: emailId },
    });
    const prismaWithTenant = getPrismaWithTenant(userlookup?.tenantId as string);
    console.log("Checking if email is active:", emailId);

    const user = await prismaWithTenant.user.findFirst({
      where: { emailId: emailId },
    });

    console.log("User found:", user);

    // Check if user status is 'active' (not pending_verification or inactive)
    return user ? user.status === "active" : false;
  }
}
