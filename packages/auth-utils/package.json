{"name": "@repo/auth-utils", "version": "0.0.0", "type": "module", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "lint": "eslint src/", "check-types": "tsc --noEmit", "test": "jest"}, "jest": {"preset": "@repo/jest-presets/node"}, "keywords": [], "author": "", "license": "ISC", "description": "Authentication utilities for SuperTokens integration and email services", "devDependencies": {"@repo/typescript-config": "workspace:^", "@types/node": "^24.3.1", "@types/express": "^5.0.3", "ts-node": "^10.9.2", "tsup": "^8.5.0", "@jest/globals": "^29.7.0", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "bunchee": "^6.4.0", "eslint": "^9.31.0", "jest": "^29.7.0", "typescript": "5.8.2"}, "dependencies": {"supertokens-node": "^21.0.2", "jsonwebtoken": "^9.0.2", "@types/jsonwebtoken": "^9.0.7"}}