import { log } from "@repo/logger";
import { z } from "zod";

// Sign In Schema
export const SignInSchema = z
  .object({
    formFields: z
      .array(
        z.object({
          id: z.string().meta({ description: "Field identifier (email or password)" }),
          value: z.string().meta({ description: "Field value" }),
        })
      )
      .length(2)
      .meta({ description: "Array of form fields containing email and password" }),
  })
  .meta({
    description: "Sign in request payload",
    example: {
      formFields: [
        { id: "email", value: "<EMAIL>" },
        { id: "password", value: "password123" },
      ],
    },
  });

// Sign Up Schema
export const SignUpSchema = z
  .object({
    formFields: z
      .array(
        z.object({
          id: z.string().meta({ description: "Field identifier" }),
          value: z.string().meta({ description: "Field value" }),
        })
      )
      .meta({ description: "Array of form fields for user registration" }),
  })
  .meta({
    description: "Sign up request payload",
    example: {
      formFields: [
        { id: "email", value: "<EMAIL>" },
        { id: "password", value: "password123" },
      ],
    },
  });

// Email Schema
export const EmailSchema = z
  .object({
    email: z.email().meta({
      description: "Valid email address",
      example: "<EMAIL>",
    }),
  })
  .meta({
    description: "Email request payload",
  });

// Password Reset Schema
export const PasswordResetSchema = z
  .object({
    email: z.email().meta({
      description: "Email address for password reset",
      example: "<EMAIL>",
    }),
  })
  .meta({
    description: "Password reset request payload",
  });

// Change Password Schema
export const ChangePasswordSchema = z.object({
  oldPassword: z.string().min(1),
  newPassword: z.string().min(8),
});

// User Metadata Schema
export const UserMetadataSchema = z
  .object({
    firstName: z.string().optional().meta({ description: "User's first name", example: "John" }),
    lastName: z.string().optional().meta({ description: "User's last name", example: "Doe" }),
    displayName: z
      .string()
      .optional()
      .meta({ description: "User's display name", example: "John Doe" }),
    role: z.string().optional().meta({ description: "User's role", example: "admin" }),
    tenantId: z
      .string()
      .optional()
      .meta({ description: "Tenant identifier", example: "tenant_123" }),
    isActive: z
      .boolean()
      .optional()
      .meta({ description: "Whether the user is active", example: true }),
    avatar: z
      .string()
      .url()
      .optional()
      .meta({ description: "User's avatar URL", example: "https://example.com/avatar.jpg" }),
    phone: z
      .string()
      .optional()
      .meta({ description: "User's phone number", example: "+1234567890" }),
    department: z
      .string()
      .optional()
      .meta({ description: "User's department", example: "Engineering" }),
    jobTitle: z
      .string()
      .optional()
      .meta({ description: "User's job title", example: "Software Engineer" }),
  })
  .meta({
    description: "User metadata",
  });

// Update User Metadata Schema
export const UpdateUserMetadataSchema = UserMetadataSchema.partial().meta({
  description: "User metadata update payload",
  example: {
    firstName: "John",
    lastName: "Doe",
    displayName: "John Doe",
    role: "admin",
    tenantId: "tenant_123",
    isActive: true,
    avatar: "https://example.com/avatar.jpg",
    phone: "+1234567890",
    department: "Engineering",
    jobTitle: "Software Engineer",
  },
});

// Auth Response Schema
export const AuthResponseSchema = z.object({
  status: z.string(),
  user: z
    .object({
      id: z.string(),
      email: z.string().email(),
      timeJoined: z.number(),
      metadata: UserMetadataSchema.optional(),
    })
    .optional(),
  message: z.string().optional(),
});

// Email Verification Schema
export const EmailVerificationSchema = z
  .object({
    token: z.string().meta({
      description: "Email verification token",
      example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    }),
  })
  .meta({
    description: "Email verification request payload",
  });

// TypeScript types inferred from schemas
export type SignInInput = z.infer<typeof SignInSchema>;
export type SignUpInput = z.infer<typeof SignUpSchema>;
export type EmailInput = z.infer<typeof EmailSchema>;
export type PasswordResetInput = z.infer<typeof PasswordResetSchema>;
export type ChangePasswordInput = z.infer<typeof ChangePasswordSchema>;
export type UserMetadata = z.infer<typeof UserMetadataSchema>;
export type UpdateUserMetadataInput = z.infer<typeof UpdateUserMetadataSchema>;
export type AuthResponse = z.infer<typeof AuthResponseSchema>;
export type EmailVerificationInput = z.infer<typeof EmailVerificationSchema>;

// Form field helper types
export interface FormField {
  id: string;
  value: string;
}

export interface AuthFormFields {
  email: string;
  password: string;
  [key: string]: string;
}

// Convert form fields array to object
export function parseFormFields(formFields: FormField[]): AuthFormFields {
  log("Parsing form fields", { formFields });

  if (!formFields || !Array.isArray(formFields)) {
    log("Invalid form fields provided, returning empty object", {
      formFields,
    });
    return {} as AuthFormFields;
  }

  return formFields.reduce((acc, field) => {
    if (field && field.id && typeof field.value === "string") {
      acc[field.id] = field.value;
    }
    return acc;
  }, {} as AuthFormFields);
}
